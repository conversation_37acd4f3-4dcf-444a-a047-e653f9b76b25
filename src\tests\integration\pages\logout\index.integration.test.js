/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import LogoutPage from 'src/pages/logout/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock dependencies
jest.mock('axios');

// Mock localStorage
const mockLocalStorage = {
  removeItem: jest.fn(),
  setItem: jest.fn(),
  getItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock window.location
delete window.location;
window.location = { href: '', assign: jest.fn(), reload: jest.fn() };

const mockedAxios = axios;

describe('Logout Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    window.location.href = '';
    mockedAxios.post.mockClear();
  });

  describe('Complete Logout Flow', () => {
    it('should display logout success message and navigation options', () => {
      renderWithProviders(<LogoutPage />);

      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Login' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Create New Account' })).toBeInTheDocument();
    });

    it('should navigate to login page when login button is clicked', async () => {
      renderWithProviders(<LogoutPage />);

      const loginButton = screen.getByRole('button', { name: 'Login' });
      const loginLink = loginButton.closest('a');
      
      expect(loginLink).toHaveAttribute('href', '/login');
    });

    it('should handle complete registration flow from logout page', async () => {
      renderWithProviders(<LogoutPage />);

      // Open registration menu
      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
      });

      // Select donor registration
      const donorOption = screen.getByText('Sign Up as Donor');
      await user.click(donorOption);

      // Verify localStorage and navigation
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'donor');
      expect(window.location.href).toBe('/register?role=donor');

      // Menu should close after selection
      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });

    it('should handle NGO registration selection', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
      });

      const ngoOption = screen.getByText('Sign Up as NGO');
      await user.click(ngoOption);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'ngo');
      expect(window.location.href).toBe('/register?role=ngo');
    });
  });

  describe('Menu Interaction Flow', () => {
    it('should handle menu open and close cycle', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Initially menu should be closed
      expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();

      // Open menu
      await user.click(createAccountButton);
      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
      });

      // Close menu by clicking outside
      await user.click(document.body);
      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });

    it('should handle menu positioning correctly', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Mock getBoundingClientRect for positioning
      const mockRect = {
        bottom: 100,
        left: 50,
        right: 200,
        top: 80,
        width: 150,
        height: 20,
      };
      
      createAccountButton.getBoundingClientRect = jest.fn(() => mockRect);

      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument();
      });

      expect(createAccountButton.getBoundingClientRect).toHaveBeenCalled();
    });

    it('should handle multiple menu interactions', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Open and close menu multiple times
      for (let i = 0; i < 3; i++) {
        await user.click(createAccountButton);
        await waitFor(() => {
          expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
        });

        await user.click(document.body);
        await waitFor(() => {
          expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
        });
      }
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle localStorage errors gracefully', async () => {
      // Mock localStorage to throw error
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage not available');
      });

      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Should not crash when localStorage fails
      const donorOption = screen.getByText('Sign Up as Donor');
      expect(() => user.click(donorOption)).not.toThrow();
    });

    it('should handle getBoundingClientRect errors', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Mock getBoundingClientRect to throw error
      createAccountButton.getBoundingClientRect = jest.fn(() => {
        throw new Error('getBoundingClientRect failed');
      });

      // Should not crash when positioning fails
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });

    it('should handle window.location assignment errors', async () => {
      // Mock window.location to throw error
      Object.defineProperty(window, 'location', {
        value: {
          href: '',
          assign: jest.fn(() => {
            throw new Error('Navigation failed');
          }),
        },
        writable: true,
      });

      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Should not crash when navigation fails
      const donorOption = screen.getByText('Sign Up as Donor');
      expect(() => user.click(donorOption)).not.toThrow();
    });
  });

  describe('Accessibility Integration', () => {
    it('should support keyboard navigation through all elements', async () => {
      renderWithProviders(<LogoutPage />);

      const loginButton = screen.getByRole('button', { name: 'Login' });
      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Tab through elements
      loginButton.focus();
      expect(loginButton).toHaveFocus();

      await user.tab();
      expect(createAccountButton).toHaveFocus();

      // Open menu with keyboard
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Navigate through menu items
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(2);
    });

    it('should support menu navigation with arrow keys', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByRole('menu')).toBeInTheDocument();
      });

      const menuItems = screen.getAllByRole('menuitem');
      
      // First item should be focusable
      menuItems[0].focus();
      expect(menuItems[0]).toHaveFocus();

      // Arrow down to next item
      await user.keyboard('{ArrowDown}');
      expect(menuItems[1]).toHaveFocus();

      // Arrow up to previous item
      await user.keyboard('{ArrowUp}');
      expect(menuItems[0]).toHaveFocus();
    });

    it('should close menu with Escape key', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Press Escape to close menu
      await user.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });

    it('should have proper ARIA attributes', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        const menu = screen.getByRole('menu');
        expect(menu).toBeInTheDocument();
        
        const menuItems = screen.getAllByRole('menuitem');
        expect(menuItems).toHaveLength(2);
        
        menuItems.forEach(item => {
          expect(item).toHaveAttribute('role', 'menuitem');
        });
      });
    });
  });

  describe('Performance Integration', () => {
    it('should handle rapid menu interactions without performance issues', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Rapidly open and close menu
      const startTime = performance.now();
      
      for (let i = 0; i < 10; i++) {
        await user.click(createAccountButton);
        await waitFor(() => {
          expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
        });
        
        await user.click(document.body);
        await waitFor(() => {
          expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
        });
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (5 seconds)
      expect(duration).toBeLessThan(5000);
    });

    it('should handle multiple simultaneous interactions', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Simulate multiple rapid clicks
      const clickPromises = [];
      for (let i = 0; i < 5; i++) {
        clickPromises.push(user.click(createAccountButton));
      }

      await Promise.all(clickPromises);

      // Should still show menu correctly
      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });
  });

  describe('Real-world Usage Scenarios', () => {
    it('should handle user changing mind during registration selection', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Open menu
      await user.click(createAccountButton);
      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Hover over donor option but don't click
      const donorOption = screen.getByText('Sign Up as Donor');
      await user.hover(donorOption);

      // Change mind and click NGO instead
      const ngoOption = screen.getByText('Sign Up as NGO');
      await user.click(ngoOption);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'ngo');
      expect(window.location.href).toBe('/register?role=ngo');
    });

    it('should handle user returning to logout page multiple times', async () => {
      const { rerender } = renderWithProviders(<LogoutPage />);

      // First visit
      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();

      // Simulate navigation away and back
      rerender(<div>Other page</div>);
      rerender(<LogoutPage />);

      // Should still work correctly
      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();
      
      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });

    it('should handle browser back/forward navigation', async () => {
      renderWithProviders(<LogoutPage />);

      // Simulate browser navigation
      const popstateEvent = new PopStateEvent('popstate', { state: {} });
      window.dispatchEvent(popstateEvent);

      // Page should still function correctly
      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();
      
      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });
  });

  describe('Component Lifecycle Integration', () => {
    it('should cleanup properly on unmount', () => {
      const { unmount } = renderWithProviders(<LogoutPage />);
      
      expect(() => unmount()).not.toThrow();
    });

    it('should handle re-mounting correctly', () => {
      const { unmount, rerender } = renderWithProviders(<LogoutPage />);
      
      unmount();
      
      expect(() => {
        rerender(<LogoutPage />);
      }).not.toThrow();

      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();
    });
  });
});
