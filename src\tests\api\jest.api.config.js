const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: '../../../'
})

// Add custom config for API tests
const apiJestConfig = {
  displayName: 'API Tests',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  testEnvironment: 'node', // Use node environment for API tests
  testMatch: [
    '<rootDir>/src/tests/api/**/*.api.test.js',
    '<rootDir>/src/tests/api/**/*.api.test.jsx'
  ],
  collectCoverageFrom: [
    'src/pages/api/**/*.{js,jsx}',
    'src/lib/api/**/*.{js,jsx}',
    'src/utils/api/**/*.{js,jsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx}',
    '!src/**/*.test.{js,jsx}',
    '!src/tests/**/*',
  ],
  coverageDirectory: '<rootDir>/coverage/api',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/@core/$1',
    '^@fake-db/(.*)$': '<rootDir>/src/@fake-db/$1',
  },
  testTimeout: 10000, // 10 seconds timeout for API tests
  setupFiles: ['<rootDir>/src/tests/api/setup/apiSetup.js'],
  globalSetup: '<rootDir>/src/tests/api/setup/globalApiSetup.js',
  globalTeardown: '<rootDir>/src/tests/api/setup/globalApiTeardown.js',
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  // Verbose output for API tests
  verbose: true,
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(apiJestConfig)
