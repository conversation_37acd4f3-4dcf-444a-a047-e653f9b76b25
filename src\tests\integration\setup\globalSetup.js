// Global setup for integration tests
module.exports = async () => {
  console.log('🚀 Setting up integration test environment...');
  
  // Set environment variables for integration tests
  process.env.NODE_ENV = 'test';
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';
  process.env.NEXT_PUBLIC_APP_ENV = 'test';
  
  // Mock external services
  process.env.MOCK_EXTERNAL_APIS = 'true';
  
  // Database setup (if needed)
  // You could set up a test database here
  
  // Start any required services
  // You could start mock servers here
  
  console.log('✅ Integration test environment setup complete');
};
