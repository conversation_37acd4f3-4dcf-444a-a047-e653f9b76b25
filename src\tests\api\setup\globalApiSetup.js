// Global setup for API tests
module.exports = async () => {
  console.log('🚀 Setting up API test environment...');
  
  // Set environment variables for API tests
  process.env.NODE_ENV = 'test';
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';
  process.env.API_SECRET_KEY = 'test-secret-key';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.DATABASE_URL = 'test-database-url';
  
  // Mock external API services
  process.env.MOCK_EXTERNAL_APIS = 'true';
  process.env.SUPPRESS_API_LOGS = 'true';
  
  // Set up test database if needed
  // You could set up a test database here
  
  // Start any required mock services
  // You could start mock API servers here
  
  console.log('✅ API test environment setup complete');
};
