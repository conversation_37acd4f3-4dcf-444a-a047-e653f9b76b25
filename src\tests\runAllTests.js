#!/usr/bin/env node

/**
 * Test Runner Script
 * Runs all test suites (unit, integration, and API tests) and provides a comprehensive report
 */

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test suite configurations
const testSuites = [
  {
    name: 'Unit Tests',
    command: 'npm run test:unit -- --coverage --watchAll=false',
    description: 'Component and utility function unit tests',
  },
  {
    name: 'Integration Tests',
    command: 'npm run test:integration -- --coverage --watchAll=false',
    description: 'End-to-end integration tests for donation modules',
  },
  {
    name: 'API Tests',
    command: 'npm run test:api -- --coverage --watchAll=false',
    description: 'API endpoint and service tests',
  },
];

// Helper functions
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  const border = '='.repeat(60);
  log(`\n${border}`, colors.cyan);
  log(`${message}`, colors.cyan + colors.bright);
  log(`${border}`, colors.cyan);
}

function logSubHeader(message) {
  log(`\n${colors.yellow}${colors.bright}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
}

function runCommand(command, description) {
  try {
    logInfo(`Running: ${description}`);
    log(`Command: ${command}`, colors.magenta);
    
    const output = execSync(command, {
      cwd: process.cwd(),
      encoding: 'utf8',
      stdio: 'pipe',
    });
    
    return {
      success: true,
      output,
      error: null,
    };
  } catch (error) {
    return {
      success: false,
      output: error.stdout || '',
      error: error.stderr || error.message,
    };
  }
}

function parseTestResults(output) {
  const results = {
    testSuites: { passed: 0, failed: 0, total: 0 },
    tests: { passed: 0, failed: 0, total: 0 },
    coverage: null,
    time: null,
  };

  // Parse test suite results
  const testSuiteMatch = output.match(/Test Suites:\s*(\d+)\s*passed(?:,\s*(\d+)\s*failed)?(?:,\s*(\d+)\s*total)?/);
  if (testSuiteMatch) {
    results.testSuites.passed = parseInt(testSuiteMatch[1]) || 0;
    results.testSuites.failed = parseInt(testSuiteMatch[2]) || 0;
    results.testSuites.total = parseInt(testSuiteMatch[3]) || results.testSuites.passed + results.testSuites.failed;
  }

  // Parse test results
  const testMatch = output.match(/Tests:\s*(\d+)\s*passed(?:,\s*(\d+)\s*failed)?(?:,\s*(\d+)\s*total)?/);
  if (testMatch) {
    results.tests.passed = parseInt(testMatch[1]) || 0;
    results.tests.failed = parseInt(testMatch[2]) || 0;
    results.tests.total = parseInt(testMatch[3]) || results.tests.passed + results.tests.failed;
  }

  // Parse execution time
  const timeMatch = output.match(/Time:\s*([\d.]+)\s*s/);
  if (timeMatch) {
    results.time = parseFloat(timeMatch[1]);
  }

  // Parse coverage (basic parsing)
  const coverageMatch = output.match(/All files\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)\s*\|\s*([\d.]+)/);
  if (coverageMatch) {
    results.coverage = {
      statements: parseFloat(coverageMatch[1]),
      branches: parseFloat(coverageMatch[2]),
      functions: parseFloat(coverageMatch[3]),
      lines: parseFloat(coverageMatch[4]),
    };
  }

  return results;
}

function displayResults(suiteName, results) {
  logSubHeader(`${suiteName} Results:`);
  
  if (results.success) {
    const parsed = parseTestResults(results.output);
    
    logSuccess(`Test Suites: ${parsed.testSuites.passed}/${parsed.testSuites.total} passed`);
    logSuccess(`Tests: ${parsed.tests.passed}/${parsed.tests.total} passed`);
    
    if (parsed.tests.failed > 0) {
      logError(`Failed Tests: ${parsed.tests.failed}`);
    }
    
    if (parsed.time) {
      logInfo(`Execution Time: ${parsed.time}s`);
    }
    
    if (parsed.coverage) {
      logInfo(`Coverage: ${parsed.coverage.statements}% statements, ${parsed.coverage.lines}% lines`);
    }
  } else {
    logError(`${suiteName} failed to run`);
    if (results.error) {
      log(`Error: ${results.error}`, colors.red);
    }
  }
}

function generateSummaryReport(allResults) {
  logHeader('📊 COMPREHENSIVE TEST SUMMARY REPORT');
  
  let totalTestSuites = { passed: 0, failed: 0, total: 0 };
  let totalTests = { passed: 0, failed: 0, total: 0 };
  let totalTime = 0;
  let successfulSuites = 0;
  
  allResults.forEach((result, index) => {
    if (result.success) {
      successfulSuites++;
      const parsed = parseTestResults(result.output);
      
      totalTestSuites.passed += parsed.testSuites.passed;
      totalTestSuites.failed += parsed.testSuites.failed;
      totalTestSuites.total += parsed.testSuites.total;
      
      totalTests.passed += parsed.tests.passed;
      totalTests.failed += parsed.tests.failed;
      totalTests.total += parsed.tests.total;
      
      if (parsed.time) {
        totalTime += parsed.time;
      }
    }
  });
  
  // Overall status
  const overallSuccess = successfulSuites === testSuites.length && totalTests.failed === 0;
  
  if (overallSuccess) {
    logSuccess('🎉 ALL TESTS PASSED!');
  } else {
    logError('❌ Some tests failed or suites had errors');
  }
  
  // Summary statistics
  logSubHeader('📈 Summary Statistics:');
  log(`Test Suites: ${totalTestSuites.passed}/${totalTestSuites.total} passed (${totalTestSuites.failed} failed)`);
  log(`Total Tests: ${totalTests.passed}/${totalTests.total} passed (${totalTests.failed} failed)`);
  log(`Success Rate: ${((totalTests.passed / totalTests.total) * 100).toFixed(2)}%`);
  log(`Total Execution Time: ${totalTime.toFixed(2)}s`);
  log(`Test Suites Run: ${successfulSuites}/${testSuites.length}`);
  
  // Breakdown by test type
  logSubHeader('📋 Breakdown by Test Type:');
  testSuites.forEach((suite, index) => {
    const result = allResults[index];
    if (result && result.success) {
      const parsed = parseTestResults(result.output);
      log(`${suite.name}: ${parsed.tests.passed}/${parsed.tests.total} tests passed`);
    } else {
      log(`${suite.name}: Failed to run`, colors.red);
    }
  });
  
  // Recommendations
  logSubHeader('💡 Recommendations:');
  if (totalTests.failed > 0) {
    logWarning(`Fix ${totalTests.failed} failing test(s) to improve reliability`);
  }
  if (successfulSuites < testSuites.length) {
    logWarning(`${testSuites.length - successfulSuites} test suite(s) failed to run - check configuration`);
  }
  if (overallSuccess) {
    logSuccess('All tests are passing! Consider adding more edge case tests.');
  }
  
  return overallSuccess;
}

// Main execution
async function main() {
  logHeader('🚀 RUNNING COMPREHENSIVE TEST SUITE');
  logInfo('This will run all unit, integration, and API tests with coverage reports');
  
  const startTime = Date.now();
  const allResults = [];
  
  // Run each test suite
  for (let i = 0; i < testSuites.length; i++) {
    const suite = testSuites[i];
    
    logSubHeader(`Running ${suite.name} (${i + 1}/${testSuites.length})`);
    logInfo(suite.description);
    
    const result = runCommand(suite.command, suite.name);
    allResults.push(result);
    
    displayResults(suite.name, result);
    
    // Add a small delay between test suites
    if (i < testSuites.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  const endTime = Date.now();
  const totalExecutionTime = ((endTime - startTime) / 1000).toFixed(2);
  
  // Generate comprehensive summary
  const overallSuccess = generateSummaryReport(allResults);
  
  logSubHeader('⏱️  Total Execution Time:');
  logInfo(`${totalExecutionTime}s`);
  
  logHeader('🏁 TEST EXECUTION COMPLETE');
  
  // Exit with appropriate code
  process.exit(overallSuccess ? 0 : 1);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  logError(`Unhandled rejection: ${error.message}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  logError(`Test runner failed: ${error.message}`);
  process.exit(1);
});
