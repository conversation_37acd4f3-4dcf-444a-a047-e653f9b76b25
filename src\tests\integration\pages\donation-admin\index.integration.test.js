/**
 * @jest-environment jsdom
 */

import React from 'react';
import { screen, waitFor, fireEvent, within, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock axios for API calls
jest.mock('axios');
const mockedAxios = axios;

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    pathname: '/donation-admin',
    query: {},
    asPath: '/donation-admin',
    route: '/donation-admin',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
}));

// Mock ApexCharts
jest.mock('react-apexcharts', () => ({
  __esModule: true,
  default: ({ options, series, type, height, ...props }) => (
    <div 
      data-testid="apex-chart"
      data-chart-type={type}
      data-chart-height={height}
      {...props}
    >
      Mock Chart - {type} - {series?.[0]?.name || 'No Data'}
    </div>
  ),
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  Card: ({ children, ...props }) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div data-testid="card-content" {...props}>{children}</div>,
  Typography: ({ children, variant, ...props }) => (
    <div data-testid={`typography-${variant || 'body1'}`} {...props}>{children}</div>
  ),
  Grid: ({ children, ...props }) => <div data-testid="grid" {...props}>{children}</div>,
  Paper: ({ children, ...props }) => <div data-testid="paper" {...props}>{children}</div>,
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  Select: ({ children, value, onChange, ...props }) => (
    <select value={value} onChange={onChange} {...props}>{children}</select>
  ),
  MenuItem: ({ children, value, ...props }) => (
    <option value={value} {...props}>{children}</option>
  ),
}));

// Integration Test Component for Donation Admin Dashboard
const IntegratedDonationAdminPage = () => {
  const [loading, setLoading] = React.useState(true);
  const [dashboardData, setDashboardData] = React.useState(null);
  const [selectedPeriod, setSelectedPeriod] = React.useState('monthly');
  const [chartData, setChartData] = React.useState(null);
  const [recentDonations, setRecentDonations] = React.useState([]);
  const [organizations, setOrganizations] = React.useState([]);
  const [exportLoading, setExportLoading] = React.useState(false);

  // Simulate API calls
  const fetchDashboardData = React.useCallback(async () => {
    setLoading(true);
    try {
      const [dashboardResponse, chartResponse, donationsResponse, orgsResponse] = await Promise.all([
        mockedAxios.get('/api/admin/dashboard'),
        mockedAxios.get(`/api/admin/charts?period=${selectedPeriod}`),
        mockedAxios.get('/api/admin/recent-donations'),
        mockedAxios.get('/api/admin/organizations'),
      ]);

      setDashboardData(dashboardResponse.data);
      setChartData(chartResponse.data);
      setRecentDonations(donationsResponse.data);
      setOrganizations(orgsResponse.data);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Set default data on error
      setDashboardData({
        totalDonations: 0,
        totalAmount: 0,
        activeOrganizations: 0,
        growthRate: 0,
      });
      setChartData({ series: [], categories: [] });
      setRecentDonations([]);
      setOrganizations([]);
    } finally {
      setLoading(false);
    }
  }, [selectedPeriod]);

  const exportData = async (format) => {
    setExportLoading(true);
    try {
      const response = await mockedAxios.post('/api/admin/export', {
        format,
        period: selectedPeriod,
      });
      
      // Simulate file download
      const blob = new Blob([response.data], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `admin-report-${selectedPeriod}.${format}`;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExportLoading(false);
    }
  };

  const refreshData = () => {
    fetchDashboardData();
  };

  React.useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  if (loading) {
    return <div data-testid="loading-spinner">Loading Admin Dashboard...</div>;
  }

  return (
    <div data-testid="donation-admin-page">
      <h1>Donation Admin Dashboard</h1>

      {/* Controls */}
      <div data-testid="dashboard-controls">
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
          data-testid="period-selector"
        >
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="monthly">Monthly</option>
          <option value="yearly">Yearly</option>
        </select>
        <button onClick={refreshData} data-testid="refresh-button">
          Refresh
        </button>
        <button 
          onClick={() => exportData('csv')} 
          disabled={exportLoading}
          data-testid="export-csv-button"
        >
          {exportLoading ? 'Exporting...' : 'Export CSV'}
        </button>
        <button 
          onClick={() => exportData('pdf')} 
          disabled={exportLoading}
          data-testid="export-pdf-button"
        >
          {exportLoading ? 'Exporting...' : 'Export PDF'}
        </button>
      </div>

      {/* Statistics Cards */}
      <div data-testid="statistics-section">
        <div data-testid="stat-card-donations">
          <h3>Total Donations</h3>
          <span data-testid="total-donations-value">
            {dashboardData?.totalDonations || 0}
          </span>
        </div>
        <div data-testid="stat-card-amount">
          <h3>Total Amount</h3>
          <span data-testid="total-amount-value">
            ${dashboardData?.totalAmount?.toLocaleString() || 0}
          </span>
        </div>
        <div data-testid="stat-card-organizations">
          <h3>Active Organizations</h3>
          <span data-testid="active-organizations-value">
            {dashboardData?.activeOrganizations || 0}
          </span>
        </div>
        <div data-testid="stat-card-growth">
          <h3>Growth Rate</h3>
          <span data-testid="growth-rate-value">
            {dashboardData?.growthRate || 0}%
          </span>
        </div>
      </div>

      {/* Charts Section */}
      <div data-testid="charts-section">
        <div data-testid="donations-chart-container">
          <h3>Donations Trend</h3>
          <div 
            data-testid="apex-chart"
            data-chart-type="line"
            data-chart-height="400"
          >
            Mock Chart - line - {chartData?.series?.[0]?.name || 'Donations'}
          </div>
        </div>
        
        <div data-testid="organizations-chart-container">
          <h3>Organizations Distribution</h3>
          <div 
            data-testid="apex-chart"
            data-chart-type="pie"
            data-chart-height="300"
          >
            Mock Chart - pie - Organizations
          </div>
        </div>
      </div>

      {/* Recent Donations Table */}
      <div data-testid="recent-donations-section">
        <h3>Recent Donations</h3>
        <div data-testid="donations-table">
          {recentDonations.length > 0 ? (
            <table>
              <thead>
                <tr>
                  <th>Donor</th>
                  <th>Amount</th>
                  <th>Organization</th>
                  <th>Date</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {recentDonations.map((donation, index) => (
                  <tr key={donation.id || index} data-testid={`donation-row-${donation.id || index}`}>
                    <td data-testid={`donor-${donation.id || index}`}>{donation.donorName}</td>
                    <td data-testid={`amount-${donation.id || index}`}>${donation.amount}</td>
                    <td data-testid={`org-${donation.id || index}`}>{donation.organizationName}</td>
                    <td data-testid={`date-${donation.id || index}`}>{donation.date}</td>
                    <td data-testid={`status-${donation.id || index}`}>
                      <span className={`status-${donation.status.toLowerCase()}`}>
                        {donation.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div data-testid="no-donations-message">No recent donations found</div>
          )}
        </div>
      </div>

      {/* Organizations Management */}
      <div data-testid="organizations-section">
        <h3>Organizations Overview</h3>
        <div data-testid="organizations-list">
          {organizations.length > 0 ? (
            organizations.map((org, index) => (
              <div key={org.id || index} data-testid={`org-item-${org.id || index}`}>
                <span data-testid={`org-name-${org.id || index}`}>{org.name}</span>
                <span data-testid={`org-donations-${org.id || index}`}>
                  {org.totalDonations} donations
                </span>
                <span data-testid={`org-amount-${org.id || index}`}>
                  ${org.totalAmount?.toLocaleString()}
                </span>
                <span data-testid={`org-status-${org.id || index}`}>
                  {org.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
            ))
          ) : (
            <div data-testid="no-organizations-message">No organizations found</div>
          )}
        </div>
      </div>

      {/* Admin Actions */}
      <div data-testid="admin-actions">
        <button data-testid="manage-users-button">Manage Users</button>
        <button data-testid="system-settings-button">System Settings</button>
        <button data-testid="audit-logs-button">Audit Logs</button>
        <button data-testid="backup-data-button">Backup Data</button>
      </div>
    </div>
  );
};

describe('Donation Admin Page - Integration Tests', () => {
  const user = userEvent.setup();

  // Mock data
  const mockDashboardData = {
    totalDonations: 1500,
    totalAmount: 75000,
    activeOrganizations: 30,
    growthRate: 12.5,
  };

  const mockChartData = {
    series: [
      { name: 'Donations', data: [100, 150, 200, 180, 220, 250] },
      { name: 'Amount', data: [5000, 7500, 10000, 9000, 11000, 12500] },
    ],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  };

  const mockRecentDonations = [
    {
      id: '1',
      donorName: 'John Doe',
      amount: 100,
      organizationName: 'Education Foundation',
      date: '2024-01-15',
      status: 'Completed',
    },
    {
      id: '2',
      donorName: 'Jane Smith',
      amount: 250,
      organizationName: 'Healthcare Support',
      date: '2024-01-14',
      status: 'Pending',
    },
    {
      id: '3',
      donorName: 'Bob Wilson',
      amount: 500,
      organizationName: 'Food Aid Program',
      date: '2024-01-13',
      status: 'Completed',
    },
  ];

  const mockOrganizations = [
    {
      id: '1',
      name: 'Education Foundation',
      totalDonations: 150,
      totalAmount: 15000,
      isActive: true,
    },
    {
      id: '2',
      name: 'Healthcare Support',
      totalDonations: 200,
      totalAmount: 25000,
      isActive: true,
    },
    {
      id: '3',
      name: 'Food Aid Program',
      totalDonations: 100,
      totalAmount: 10000,
      isActive: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default API responses
    mockedAxios.get.mockImplementation((url) => {
      if (url === '/api/admin/dashboard') {
        return Promise.resolve({ data: mockDashboardData });
      }
      if (url.startsWith('/api/admin/charts')) {
        return Promise.resolve({ data: mockChartData });
      }
      if (url === '/api/admin/recent-donations') {
        return Promise.resolve({ data: mockRecentDonations });
      }
      if (url === '/api/admin/organizations') {
        return Promise.resolve({ data: mockOrganizations });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });

    mockedAxios.post.mockResolvedValue({ 
      data: 'mock-export-data',
      headers: { 'content-type': 'application/octet-stream' }
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Dashboard Loading and Initial State', () => {
    it('should load the admin dashboard and fetch all data on mount', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      // Should show loading initially
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      // Should fetch all data
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=monthly');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/recent-donations');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/organizations');
      });

      // Should display dashboard content
      await waitFor(() => {
        expect(screen.getByText('Donation Admin Dashboard')).toBeInTheDocument();
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Should show default values
      expect(screen.getByTestId('total-donations-value')).toHaveTextContent('0');
      expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$0');
      expect(screen.getByTestId('active-organizations-value')).toHaveTextContent('0');
      expect(screen.getByTestId('growth-rate-value')).toHaveTextContent('0%');
    });

    it('should display all dashboard sections', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-controls')).toBeInTheDocument();
        expect(screen.getByTestId('statistics-section')).toBeInTheDocument();
        expect(screen.getByTestId('charts-section')).toBeInTheDocument();
        expect(screen.getByTestId('recent-donations-section')).toBeInTheDocument();
        expect(screen.getByTestId('organizations-section')).toBeInTheDocument();
        expect(screen.getByTestId('admin-actions')).toBeInTheDocument();
      });
    });
  });

  describe('Statistics Display', () => {
    it('should display correct statistics values', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('1500');
        expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$75,000');
        expect(screen.getByTestId('active-organizations-value')).toHaveTextContent('30');
        expect(screen.getByTestId('growth-rate-value')).toHaveTextContent('12.5%');
      });
    });

    it('should display all statistics cards', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('stat-card-donations')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-amount')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-organizations')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-growth')).toBeInTheDocument();
      });
    });
  });

  describe('Period Selection and Data Refresh', () => {
    it('should change period and refetch chart data', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('period-selector')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Change period
      const periodSelector = screen.getByTestId('period-selector');
      await user.selectOptions(periodSelector, 'yearly');

      // Should refetch data with new period
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=yearly');
      });
    });

    it('should refresh all data when refresh button is clicked', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Click refresh
      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      // Should refetch all data
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=monthly');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/recent-donations');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/organizations');
      });
    });
  });

  describe('Charts Display', () => {
    it('should display donations trend chart', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donations-chart-container')).toBeInTheDocument();
        expect(screen.getByText('Donations Trend')).toBeInTheDocument();
      });

      const chartElements = screen.getAllByTestId('apex-chart');
      expect(chartElements.length).toBeGreaterThan(0);
    });

    it('should display organizations distribution chart', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('organizations-chart-container')).toBeInTheDocument();
        expect(screen.getByText('Organizations Distribution')).toBeInTheDocument();
      });
    });
  });

  describe('Recent Donations Table', () => {
    it('should display recent donations table with data', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('recent-donations-section')).toBeInTheDocument();
        expect(screen.getByTestId('donations-table')).toBeInTheDocument();
      });

      // Check table data
      expect(screen.getByTestId('donation-row-1')).toBeInTheDocument();
      expect(screen.getByTestId('donor-1')).toHaveTextContent('John Doe');
      expect(screen.getByTestId('amount-1')).toHaveTextContent('$100');
      expect(screen.getByTestId('org-1')).toHaveTextContent('Education Foundation');
      expect(screen.getByTestId('status-1')).toHaveTextContent('Completed');

      expect(screen.getByTestId('donation-row-2')).toBeInTheDocument();
      expect(screen.getByTestId('donor-2')).toHaveTextContent('Jane Smith');
      expect(screen.getByTestId('amount-2')).toHaveTextContent('$250');
      expect(screen.getByTestId('status-2')).toHaveTextContent('Pending');
    });

    it('should display no donations message when table is empty', async () => {
      mockedAxios.get.mockImplementation((url) => {
        if (url === '/api/admin/recent-donations') {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: mockDashboardData });
      });

      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('no-donations-message')).toBeInTheDocument();
        expect(screen.getByText('No recent donations found')).toBeInTheDocument();
      });
    });
  });

  describe('Organizations Management', () => {
    it('should display organizations list with data', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('organizations-section')).toBeInTheDocument();
        expect(screen.getByTestId('organizations-list')).toBeInTheDocument();
      });

      // Check organizations data
      expect(screen.getByTestId('org-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('org-name-1')).toHaveTextContent('Education Foundation');
      expect(screen.getByTestId('org-donations-1')).toHaveTextContent('150 donations');
      expect(screen.getByTestId('org-amount-1')).toHaveTextContent('$15,000');
      expect(screen.getByTestId('org-status-1')).toHaveTextContent('Active');

      expect(screen.getByTestId('org-item-3')).toBeInTheDocument();
      expect(screen.getByTestId('org-status-3')).toHaveTextContent('Inactive');
    });

    it('should display no organizations message when list is empty', async () => {
      mockedAxios.get.mockImplementation((url) => {
        if (url === '/api/admin/organizations') {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: mockDashboardData });
      });

      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('no-organizations-message')).toBeInTheDocument();
        expect(screen.getByText('No organizations found')).toBeInTheDocument();
      });
    });
  });

  describe('Export Functionality', () => {
    it('should export data as CSV', async () => {
      // Mock URL.createObjectURL and related methods
      const originalCreateObjectURL = global.URL.createObjectURL;
      const originalRevokeObjectURL = global.URL.revokeObjectURL;
      const originalCreateElement = document.createElement;

      global.URL.createObjectURL = jest.fn(() => 'mock-url');
      global.URL.revokeObjectURL = jest.fn();

      // Mock document.createElement to return a proper DOM element
      const mockLink = document.createElement('a');
      mockLink.click = jest.fn();
      document.createElement = jest.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink;
        }
        return originalCreateElement.call(document, tagName);
      });

      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('export-csv-button')).toBeInTheDocument();
      });

      const exportButton = screen.getByTestId('export-csv-button');

      await act(async () => {
        await user.click(exportButton);
      });

      // Should call export API
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/admin/export', {
          format: 'csv',
          period: 'monthly',
        });
      });

      // Should trigger download
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.download).toBe('admin-report-monthly.csv');

      // Cleanup
      global.URL.createObjectURL = originalCreateObjectURL;
      global.URL.revokeObjectURL = originalRevokeObjectURL;
      document.createElement = originalCreateElement;
    });

    it('should export data as PDF', async () => {
      // Mock URL.createObjectURL and related methods
      const originalCreateObjectURL = global.URL.createObjectURL;
      const originalRevokeObjectURL = global.URL.revokeObjectURL;
      const originalCreateElement = document.createElement;

      global.URL.createObjectURL = jest.fn(() => 'mock-url');
      global.URL.revokeObjectURL = jest.fn();

      // Mock document.createElement to return a proper DOM element
      const mockLink = document.createElement('a');
      mockLink.click = jest.fn();
      document.createElement = jest.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink;
        }
        return originalCreateElement.call(document, tagName);
      });

      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('export-pdf-button')).toBeInTheDocument();
      });

      const exportButton = screen.getByTestId('export-pdf-button');

      await act(async () => {
        await user.click(exportButton);
      });

      // Should call export API
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/admin/export', {
          format: 'pdf',
          period: 'monthly',
        });
      });

      // Should trigger download
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.download).toBe('admin-report-monthly.pdf');

      // Cleanup
      global.URL.createObjectURL = originalCreateObjectURL;
      global.URL.revokeObjectURL = originalRevokeObjectURL;
      document.createElement = originalCreateElement;
    });




  });

  describe('Admin Actions', () => {
    it('should display all admin action buttons', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('admin-actions')).toBeInTheDocument();
        expect(screen.getByTestId('manage-users-button')).toBeInTheDocument();
        expect(screen.getByTestId('system-settings-button')).toBeInTheDocument();
        expect(screen.getByTestId('audit-logs-button')).toBeInTheDocument();
        expect(screen.getByTestId('backup-data-button')).toBeInTheDocument();
      });
    });

    it('should handle admin action button clicks', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('manage-users-button')).toBeInTheDocument();
      });

      // Test button interactions
      const manageUsersButton = screen.getByTestId('manage-users-button');
      await user.click(manageUsersButton);

      const systemSettingsButton = screen.getByTestId('system-settings-button');
      await user.click(systemSettingsButton);

      const auditLogsButton = screen.getByTestId('audit-logs-button');
      await user.click(auditLogsButton);

      const backupDataButton = screen.getByTestId('backup-data-button');
      await user.click(backupDataButton);

      // Buttons should be clickable (no errors thrown)
      expect(manageUsersButton).toBeInTheDocument();
      expect(systemSettingsButton).toBeInTheDocument();
      expect(auditLogsButton).toBeInTheDocument();
      expect(backupDataButton).toBeInTheDocument();
    });
  });

  describe('Data Integration', () => {
    it('should handle multiple API calls concurrently', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      // Should make all API calls concurrently
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledTimes(4);
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=monthly');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/recent-donations');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/organizations');
      });
    });

    it('should update chart data when period changes', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('period-selector')).toBeInTheDocument();
      });

      // Change period multiple times
      const periodSelector = screen.getByTestId('period-selector');

      await user.selectOptions(periodSelector, 'weekly');
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=weekly');
      });

      await user.selectOptions(periodSelector, 'daily');
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/admin/charts?period=daily');
      });
    });

    it('should maintain state consistency across operations', async () => {
      renderWithProviders(<IntegratedDonationAdminPage />);

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('1500');
      });

      // Change period
      const periodSelector = screen.getByTestId('period-selector');
      await user.selectOptions(periodSelector, 'yearly');

      // Statistics should remain consistent
      expect(screen.getByTestId('total-donations-value')).toHaveTextContent('1500');
      expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$75,000');

      // Refresh data
      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      // Data should still be consistent
      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('1500');
      });
    });
  });
});
