/**
 * @jest-environment jsdom
 */

import React from 'react';
import { screen, waitFor, fireEvent, within, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock axios for API calls
jest.mock('axios');
const mockedAxios = axios;

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    pathname: '/donation-tenant',
    query: {},
    asPath: '/donation-tenant',
    route: '/donation-tenant',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    },
  }),
}));

// Mock ApexCharts
jest.mock('react-apexcharts', () => ({
  __esModule: true,
  default: ({ options, series, type, height, ...props }) => (
    <div 
      data-testid="apex-chart"
      data-chart-type={type}
      data-chart-height={height}
      {...props}
    >
      Mock Chart - {type} - {series?.[0]?.name || 'No Data'}
    </div>
  ),
}));

// Mock Material-UI components
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  Card: ({ children, ...props }) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div data-testid="card-content" {...props}>{children}</div>,
  Typography: ({ children, variant, ...props }) => (
    <div data-testid={`typography-${variant || 'body1'}`} {...props}>{children}</div>
  ),
  Grid: ({ children, ...props }) => <div data-testid="grid" {...props}>{children}</div>,
  Paper: ({ children, ...props }) => <div data-testid="paper" {...props}>{children}</div>,
  Button: ({ children, onClick, ...props }) => (
    <button onClick={onClick} {...props}>{children}</button>
  ),
  Select: ({ children, value, onChange, ...props }) => (
    <select value={value} onChange={onChange} {...props}>{children}</select>
  ),
  MenuItem: ({ children, value, ...props }) => (
    <option value={value} {...props}>{children}</option>
  ),
}));

// Integration Test Component for Donation Tenant Dashboard
const IntegratedDonationTenantPage = () => {
  const [loading, setLoading] = React.useState(true);
  const [tenantData, setTenantData] = React.useState(null);
  const [selectedPeriod, setSelectedPeriod] = React.useState('this-month');
  const [chartData, setChartData] = React.useState(null);
  const [topDonors, setTopDonors] = React.useState([]);
  const [recentActivities, setRecentActivities] = React.useState([]);
  const [donationHeads, setDonationHeads] = React.useState([]);
  const [exportLoading, setExportLoading] = React.useState(false);
  const [selectedDonationHead, setSelectedDonationHead] = React.useState('all');

  // Simulate API calls
  const fetchTenantData = React.useCallback(async () => {
    setLoading(true);
    try {
      const [tenantResponse, chartResponse, donorsResponse, activitiesResponse, headsResponse] = await Promise.all([
        mockedAxios.get('/api/tenant/dashboard'),
        mockedAxios.get(`/api/tenant/charts?period=${selectedPeriod}&head=${selectedDonationHead}`),
        mockedAxios.get('/api/tenant/top-donors'),
        mockedAxios.get('/api/tenant/recent-activities'),
        mockedAxios.get('/api/tenant/donation-heads'),
      ]);

      setTenantData(tenantResponse.data);
      setChartData(chartResponse.data);
      setTopDonors(donorsResponse.data);
      setRecentActivities(activitiesResponse.data);
      setDonationHeads(headsResponse.data);
    } catch (error) {
      console.error('Failed to fetch tenant data:', error);
      // Set default data on error
      setTenantData({
        totalDonations: 0,
        totalAmount: 0,
        activeDonationHeads: 0,
        averageDonation: 0,
        growthRate: 0,
      });
      setChartData({ series: [], categories: [] });
      setTopDonors([]);
      setRecentActivities([]);
      setDonationHeads([]);
    } finally {
      setLoading(false);
    }
  }, [selectedPeriod, selectedDonationHead]);

  const exportTenantData = async (format) => {
    setExportLoading(true);
    try {
      const response = await mockedAxios.post('/api/tenant/export', {
        format,
        period: selectedPeriod,
        donationHead: selectedDonationHead,
      });
      
      // Simulate file download
      const blob = new Blob([response.data], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tenant-report-${selectedPeriod}.${format}`;
      link.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExportLoading(false);
    }
  };

  const refreshData = () => {
    fetchTenantData();
  };

  React.useEffect(() => {
    fetchTenantData();
  }, [fetchTenantData]);

  if (loading) {
    return <div data-testid="loading-spinner">Loading Tenant Dashboard...</div>;
  }

  return (
    <div data-testid="donation-tenant-page">
      <h1>Donation Tenant Dashboard</h1>

      {/* Controls */}
      <div data-testid="dashboard-controls">
        <select
          value={selectedPeriod}
          onChange={(e) => setSelectedPeriod(e.target.value)}
          data-testid="period-selector"
        >
          <option value="this-week">This Week</option>
          <option value="this-month">This Month</option>
          <option value="this-quarter">This Quarter</option>
          <option value="this-year">This Year</option>
        </select>
        <select
          value={selectedDonationHead}
          onChange={(e) => setSelectedDonationHead(e.target.value)}
          data-testid="donation-head-selector"
        >
          <option value="all">All Donation Heads</option>
          {donationHeads.map((head) => (
            <option key={head.id} value={head.id}>
              {head.name}
            </option>
          ))}
        </select>
        <button onClick={refreshData} data-testid="refresh-button">
          Refresh
        </button>
        <button 
          onClick={() => exportTenantData('csv')} 
          disabled={exportLoading}
          data-testid="export-csv-button"
        >
          {exportLoading ? 'Exporting...' : 'Export CSV'}
        </button>
        <button 
          onClick={() => exportTenantData('pdf')} 
          disabled={exportLoading}
          data-testid="export-pdf-button"
        >
          {exportLoading ? 'Exporting...' : 'Export PDF'}
        </button>
      </div>

      {/* Statistics Cards */}
      <div data-testid="statistics-section">
        <div data-testid="stat-card-donations">
          <h3>Total Donations</h3>
          <span data-testid="total-donations-value">
            {tenantData?.totalDonations || 0}
          </span>
        </div>
        <div data-testid="stat-card-amount">
          <h3>Total Amount</h3>
          <span data-testid="total-amount-value">
            ${tenantData?.totalAmount?.toLocaleString() || 0}
          </span>
        </div>
        <div data-testid="stat-card-heads">
          <h3>Active Donation Heads</h3>
          <span data-testid="active-heads-value">
            {tenantData?.activeDonationHeads || 0}
          </span>
        </div>
        <div data-testid="stat-card-average">
          <h3>Average Donation</h3>
          <span data-testid="average-donation-value">
            ${tenantData?.averageDonation || 0}
          </span>
        </div>
        <div data-testid="stat-card-growth">
          <h3>Growth Rate</h3>
          <span data-testid="growth-rate-value">
            {tenantData?.growthRate || 0}%
          </span>
        </div>
      </div>

      {/* Charts Section */}
      <div data-testid="charts-section">
        <div data-testid="donations-trend-container">
          <h3>Donations Trend</h3>
          <div 
            data-testid="apex-chart"
            data-chart-type="line"
            data-chart-height="400"
          >
            Mock Chart - line - {chartData?.series?.[0]?.name || 'Donations'}
          </div>
        </div>
        
        <div data-testid="donation-heads-chart-container">
          <h3>Donation Heads Performance</h3>
          <div 
            data-testid="apex-chart"
            data-chart-type="bar"
            data-chart-height="350"
          >
            Mock Chart - bar - Donation Heads
          </div>
        </div>
      </div>

      {/* Top Donors Section */}
      <div data-testid="top-donors-section">
        <h3>Top Donors</h3>
        <div data-testid="donors-list">
          {topDonors.length > 0 ? (
            topDonors.map((donor, index) => (
              <div key={donor.id || index} data-testid={`donor-item-${donor.id || index}`}>
                <span data-testid={`donor-name-${donor.id || index}`}>{donor.name}</span>
                <span data-testid={`donor-amount-${donor.id || index}`}>
                  ${donor.totalAmount?.toLocaleString()}
                </span>
                <span data-testid={`donor-count-${donor.id || index}`}>
                  {donor.donationCount} donations
                </span>
                <span data-testid={`donor-rank-${donor.id || index}`}>
                  #{index + 1}
                </span>
              </div>
            ))
          ) : (
            <div data-testid="no-donors-message">No donors found</div>
          )}
        </div>
      </div>

      {/* Recent Activities */}
      <div data-testid="recent-activities-section">
        <h3>Recent Activities</h3>
        <div data-testid="activities-list">
          {recentActivities.length > 0 ? (
            recentActivities.map((activity, index) => (
              <div key={activity.id || index} data-testid={`activity-item-${activity.id || index}`}>
                <span data-testid={`activity-type-${activity.id || index}`}>{activity.type}</span>
                <span data-testid={`activity-description-${activity.id || index}`}>
                  {activity.description}
                </span>
                <span data-testid={`activity-date-${activity.id || index}`}>{activity.date}</span>
                <span data-testid={`activity-user-${activity.id || index}`}>{activity.user}</span>
              </div>
            ))
          ) : (
            <div data-testid="no-activities-message">No recent activities</div>
          )}
        </div>
      </div>

      {/* Donation Heads Management */}
      <div data-testid="donation-heads-section">
        <h3>Donation Heads Overview</h3>
        <div data-testid="heads-list">
          {donationHeads.length > 0 ? (
            donationHeads.map((head, index) => (
              <div key={head.id || index} data-testid={`head-item-${head.id || index}`}>
                <span data-testid={`head-name-${head.id || index}`}>{head.name}</span>
                <span data-testid={`head-donations-${head.id || index}`}>
                  {head.totalDonations} donations
                </span>
                <span data-testid={`head-amount-${head.id || index}`}>
                  ${head.totalAmount?.toLocaleString()}
                </span>
                <span data-testid={`head-status-${head.id || index}`}>
                  {head.isActive ? 'Active' : 'Inactive'}
                </span>
                <button 
                  data-testid={`head-manage-${head.id || index}`}
                  onClick={() => console.log('Manage head:', head.id)}
                >
                  Manage
                </button>
              </div>
            ))
          ) : (
            <div data-testid="no-heads-message">No donation heads found</div>
          )}
        </div>
      </div>

      {/* Tenant Actions */}
      <div data-testid="tenant-actions">
        <button data-testid="create-donation-head-button">Create Donation Head</button>
        <button data-testid="manage-donors-button">Manage Donors</button>
        <button data-testid="generate-receipts-button">Generate Receipts</button>
        <button data-testid="view-reports-button">View Reports</button>
      </div>
    </div>
  );
};

describe('Donation Tenant Page - Integration Tests', () => {
  const user = userEvent.setup();

  // Mock data
  const mockTenantData = {
    totalDonations: 750,
    totalAmount: 37500,
    activeDonationHeads: 15,
    averageDonation: 50,
    growthRate: 12.5,
  };

  const mockChartData = {
    series: [
      { name: 'Donations', data: [50, 75, 100, 90, 110, 125] },
      { name: 'Amount', data: [2500, 3750, 5000, 4500, 5500, 6250] },
    ],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  };

  const mockTopDonors = [
    {
      id: '1',
      name: 'Alice Johnson',
      totalAmount: 1500,
      donationCount: 15,
    },
    {
      id: '2',
      name: 'Bob Wilson',
      totalAmount: 1200,
      donationCount: 12,
    },
    {
      id: '3',
      name: 'Carol Davis',
      totalAmount: 1000,
      donationCount: 10,
    },
  ];

  const mockRecentActivities = [
    {
      id: '1',
      type: 'Donation',
      description: 'New donation received from Alice Johnson',
      date: '2024-01-15',
      user: 'Alice Johnson',
    },
    {
      id: '2',
      type: 'Donation Head',
      description: 'Education Fund donation head created',
      date: '2024-01-14',
      user: 'Admin User',
    },
    {
      id: '3',
      type: 'Receipt',
      description: 'Receipt generated for donation #123',
      date: '2024-01-13',
      user: 'System',
    },
  ];

  const mockDonationHeads = [
    {
      id: '1',
      name: 'Education Fund',
      totalDonations: 100,
      totalAmount: 10000,
      isActive: true,
    },
    {
      id: '2',
      name: 'Healthcare Support',
      totalDonations: 150,
      totalAmount: 15000,
      isActive: true,
    },
    {
      id: '3',
      name: 'Food Aid Program',
      totalDonations: 75,
      totalAmount: 7500,
      isActive: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default API responses
    mockedAxios.get.mockImplementation((url) => {
      if (url === '/api/tenant/dashboard') {
        return Promise.resolve({ data: mockTenantData });
      }
      if (url.startsWith('/api/tenant/charts')) {
        return Promise.resolve({ data: mockChartData });
      }
      if (url === '/api/tenant/top-donors') {
        return Promise.resolve({ data: mockTopDonors });
      }
      if (url === '/api/tenant/recent-activities') {
        return Promise.resolve({ data: mockRecentActivities });
      }
      if (url === '/api/tenant/donation-heads') {
        return Promise.resolve({ data: mockDonationHeads });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });

    mockedAxios.post.mockResolvedValue({ 
      data: 'mock-export-data',
      headers: { 'content-type': 'application/octet-stream' }
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Dashboard Loading and Initial State', () => {
    it('should load the tenant dashboard and fetch all data on mount', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      // Should show loading initially
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      // Should fetch all data
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-month&head=all');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/top-donors');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/recent-activities');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads');
      });

      // Should display dashboard content
      await waitFor(() => {
        expect(screen.getByText('Donation Tenant Dashboard')).toBeInTheDocument();
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });

      // Should show default values
      expect(screen.getByTestId('total-donations-value')).toHaveTextContent('0');
      expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$0');
      expect(screen.getByTestId('active-heads-value')).toHaveTextContent('0');
      expect(screen.getByTestId('average-donation-value')).toHaveTextContent('$0');
      expect(screen.getByTestId('growth-rate-value')).toHaveTextContent('0%');
    });

    it('should display all dashboard sections', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-controls')).toBeInTheDocument();
        expect(screen.getByTestId('statistics-section')).toBeInTheDocument();
        expect(screen.getByTestId('charts-section')).toBeInTheDocument();
        expect(screen.getByTestId('top-donors-section')).toBeInTheDocument();
        expect(screen.getByTestId('recent-activities-section')).toBeInTheDocument();
        expect(screen.getByTestId('donation-heads-section')).toBeInTheDocument();
        expect(screen.getByTestId('tenant-actions')).toBeInTheDocument();
      });
    });
  });

  describe('Statistics Display', () => {
    it('should display correct tenant statistics values', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('750');
        expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$37,500');
        expect(screen.getByTestId('active-heads-value')).toHaveTextContent('15');
        expect(screen.getByTestId('average-donation-value')).toHaveTextContent('$50');
        expect(screen.getByTestId('growth-rate-value')).toHaveTextContent('12.5%');
      });
    });

    it('should display all statistics cards', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('stat-card-donations')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-amount')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-heads')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-average')).toBeInTheDocument();
        expect(screen.getByTestId('stat-card-growth')).toBeInTheDocument();
      });
    });
  });

  describe('Period and Filter Selection', () => {
    it('should change period and refetch chart data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('period-selector')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Change period
      const periodSelector = screen.getByTestId('period-selector');
      await user.selectOptions(periodSelector, 'this-year');

      // Should refetch data with new period
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-year&head=all');
      });
    });

    it('should filter by donation head and refetch data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-selector')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Change donation head filter
      const headSelector = screen.getByTestId('donation-head-selector');
      await user.selectOptions(headSelector, '1');

      // Should refetch data with new filter
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-month&head=1');
      });
    });

    it('should refresh all data when refresh button is clicked', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      // Clear previous calls
      mockedAxios.get.mockClear();

      // Click refresh
      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      // Should refetch all data
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-month&head=all');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/top-donors');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/recent-activities');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads');
      });
    });
  });

  describe('Charts Display', () => {
    it('should display donations trend chart', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donations-trend-container')).toBeInTheDocument();
        expect(screen.getByText('Donations Trend')).toBeInTheDocument();
      });

      const chartElements = screen.getAllByTestId('apex-chart');
      expect(chartElements.length).toBeGreaterThan(0);
    });

    it('should display donation heads performance chart', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-heads-chart-container')).toBeInTheDocument();
        expect(screen.getByText('Donation Heads Performance')).toBeInTheDocument();
      });
    });
  });

  describe('Top Donors Section', () => {
    it('should display top donors list with data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('top-donors-section')).toBeInTheDocument();
        expect(screen.getByTestId('donors-list')).toBeInTheDocument();
      });

      // Check donors data
      expect(screen.getByTestId('donor-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('donor-name-1')).toHaveTextContent('Alice Johnson');
      expect(screen.getByTestId('donor-amount-1')).toHaveTextContent('$1,500');
      expect(screen.getByTestId('donor-count-1')).toHaveTextContent('15 donations');
      expect(screen.getByTestId('donor-rank-1')).toHaveTextContent('#1');

      expect(screen.getByTestId('donor-item-2')).toBeInTheDocument();
      expect(screen.getByTestId('donor-name-2')).toHaveTextContent('Bob Wilson');
      expect(screen.getByTestId('donor-rank-2')).toHaveTextContent('#2');

      expect(screen.getByTestId('donor-item-3')).toBeInTheDocument();
      expect(screen.getByTestId('donor-name-3')).toHaveTextContent('Carol Davis');
      expect(screen.getByTestId('donor-rank-3')).toHaveTextContent('#3');
    });

    it('should display no donors message when list is empty', async () => {
      mockedAxios.get.mockImplementation((url) => {
        if (url === '/api/tenant/top-donors') {
          return Promise.resolve({ data: [] });
        }
        if (url === '/api/tenant/dashboard') {
          return Promise.resolve({ data: mockTenantData });
        }
        if (url === '/api/tenant/donation-heads') {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: [] });
      });

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('no-donors-message')).toBeInTheDocument();
        expect(screen.getByText('No donors found')).toBeInTheDocument();
      });
    });
  });

  describe('Recent Activities Section', () => {
    it('should display recent activities list with data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('recent-activities-section')).toBeInTheDocument();
        expect(screen.getByTestId('activities-list')).toBeInTheDocument();
      });

      // Check activities data
      expect(screen.getByTestId('activity-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('activity-type-1')).toHaveTextContent('Donation');
      expect(screen.getByTestId('activity-description-1')).toHaveTextContent('New donation received from Alice Johnson');
      expect(screen.getByTestId('activity-date-1')).toHaveTextContent('2024-01-15');
      expect(screen.getByTestId('activity-user-1')).toHaveTextContent('Alice Johnson');

      expect(screen.getByTestId('activity-item-2')).toBeInTheDocument();
      expect(screen.getByTestId('activity-type-2')).toHaveTextContent('Donation Head');

      expect(screen.getByTestId('activity-item-3')).toBeInTheDocument();
      expect(screen.getByTestId('activity-type-3')).toHaveTextContent('Receipt');
    });


  });

  describe('Donation Heads Management', () => {
    it('should display donation heads list with data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-heads-section')).toBeInTheDocument();
        expect(screen.getByTestId('heads-list')).toBeInTheDocument();
      });

      // Check donation heads data
      expect(screen.getByTestId('head-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('head-name-1')).toHaveTextContent('Education Fund');
      expect(screen.getByTestId('head-donations-1')).toHaveTextContent('100 donations');
      expect(screen.getByTestId('head-amount-1')).toHaveTextContent('$10,000');
      expect(screen.getByTestId('head-status-1')).toHaveTextContent('Active');
      expect(screen.getByTestId('head-manage-1')).toBeInTheDocument();

      expect(screen.getByTestId('head-item-3')).toBeInTheDocument();
      expect(screen.getByTestId('head-status-3')).toHaveTextContent('Inactive');
    });

    it('should handle manage button clicks for donation heads', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('head-manage-1')).toBeInTheDocument();
      });

      const manageButton = screen.getByTestId('head-manage-1');
      await user.click(manageButton);

      expect(consoleSpy).toHaveBeenCalledWith('Manage head:', '1');

      consoleSpy.mockRestore();
    });

    it('should display no donation heads message when list is empty', async () => {
      mockedAxios.get.mockImplementation((url) => {
        if (url === '/api/tenant/donation-heads') {
          return Promise.resolve({ data: [] });
        }
        return Promise.resolve({ data: mockTenantData });
      });

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('no-heads-message')).toBeInTheDocument();
        expect(screen.getByText('No donation heads found')).toBeInTheDocument();
      });
    });
  });

  describe('Export Functionality', () => {
    it('should export tenant data as CSV', async () => {
      // Mock URL.createObjectURL and related methods
      const originalCreateObjectURL = global.URL.createObjectURL;
      const originalRevokeObjectURL = global.URL.revokeObjectURL;
      const originalCreateElement = document.createElement;

      global.URL.createObjectURL = jest.fn(() => 'mock-url');
      global.URL.revokeObjectURL = jest.fn();

      // Mock document.createElement to return a proper DOM element
      const mockLink = document.createElement('a');
      mockLink.click = jest.fn();
      document.createElement = jest.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink;
        }
        return originalCreateElement.call(document, tagName);
      });

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('export-csv-button')).toBeInTheDocument();
      });

      const exportButton = screen.getByTestId('export-csv-button');

      await act(async () => {
        await user.click(exportButton);
      });

      // Should call export API
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/tenant/export', {
          format: 'csv',
          period: 'this-month',
          donationHead: 'all',
        });
      });

      // Should trigger download
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.download).toBe('tenant-report-this-month.csv');

      // Cleanup
      global.URL.createObjectURL = originalCreateObjectURL;
      global.URL.revokeObjectURL = originalRevokeObjectURL;
      document.createElement = originalCreateElement;
    });

    it('should export tenant data as PDF', async () => {
      // Mock URL.createObjectURL and related methods
      const originalCreateObjectURL = global.URL.createObjectURL;
      const originalRevokeObjectURL = global.URL.revokeObjectURL;
      const originalCreateElement = document.createElement;

      global.URL.createObjectURL = jest.fn(() => 'mock-url');
      global.URL.revokeObjectURL = jest.fn();

      // Mock document.createElement to return a proper DOM element
      const mockLink = document.createElement('a');
      mockLink.click = jest.fn();
      document.createElement = jest.fn((tagName) => {
        if (tagName === 'a') {
          return mockLink;
        }
        return originalCreateElement.call(document, tagName);
      });

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('export-pdf-button')).toBeInTheDocument();
      });

      const exportButton = screen.getByTestId('export-pdf-button');

      await act(async () => {
        await user.click(exportButton);
      });

      // Should call export API
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith('/api/tenant/export', {
          format: 'pdf',
          period: 'this-month',
          donationHead: 'all',
        });
      });

      // Should trigger download
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.download).toBe('tenant-report-this-month.pdf');

      // Cleanup
      global.URL.createObjectURL = originalCreateObjectURL;
      global.URL.revokeObjectURL = originalRevokeObjectURL;
      document.createElement = originalCreateElement;
    });

    it('should handle export errors gracefully', async () => {
      mockedAxios.post.mockRejectedValue(new Error('Export failed'));

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('export-csv-button')).toBeInTheDocument();
      });

      const exportButton = screen.getByTestId('export-csv-button');
      await user.click(exportButton);

      // Should call API but handle error gracefully
      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalled();
      });

      // Button should not be disabled after error
      expect(exportButton).not.toBeDisabled();
    });


  });

  describe('Tenant Actions', () => {
    it('should display all tenant action buttons', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('tenant-actions')).toBeInTheDocument();
        expect(screen.getByTestId('create-donation-head-button')).toBeInTheDocument();
        expect(screen.getByTestId('manage-donors-button')).toBeInTheDocument();
        expect(screen.getByTestId('generate-receipts-button')).toBeInTheDocument();
        expect(screen.getByTestId('view-reports-button')).toBeInTheDocument();
      });
    });

    it('should handle tenant action button clicks', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-donation-head-button')).toBeInTheDocument();
      });

      // Test button interactions
      const createHeadButton = screen.getByTestId('create-donation-head-button');
      await user.click(createHeadButton);

      const manageDonorsButton = screen.getByTestId('manage-donors-button');
      await user.click(manageDonorsButton);

      const generateReceiptsButton = screen.getByTestId('generate-receipts-button');
      await user.click(generateReceiptsButton);

      const viewReportsButton = screen.getByTestId('view-reports-button');
      await user.click(viewReportsButton);

      // Buttons should be clickable (no errors thrown)
      expect(createHeadButton).toBeInTheDocument();
      expect(manageDonorsButton).toBeInTheDocument();
      expect(generateReceiptsButton).toBeInTheDocument();
      expect(viewReportsButton).toBeInTheDocument();
    });
  });

  describe('Data Integration and Filtering', () => {
    it('should handle multiple API calls concurrently', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      // Should make all API calls concurrently
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledTimes(5);
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-month&head=all');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/top-donors');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/recent-activities');
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads');
      });
    });

    it('should update chart data when both period and donation head change', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('period-selector')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-selector')).toBeInTheDocument();
      });

      // Change both filters
      const periodSelector = screen.getByTestId('period-selector');
      const headSelector = screen.getByTestId('donation-head-selector');

      await user.selectOptions(periodSelector, 'this-quarter');
      await user.selectOptions(headSelector, '2');

      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-quarter&head=2');
      });
    });

    it('should maintain state consistency across operations', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('750');
      });

      // Change period
      const periodSelector = screen.getByTestId('period-selector');
      await user.selectOptions(periodSelector, 'this-year');

      // Statistics should remain consistent (dashboard data doesn't change with period)
      expect(screen.getByTestId('total-donations-value')).toHaveTextContent('750');
      expect(screen.getByTestId('total-amount-value')).toHaveTextContent('$37,500');

      // Refresh data
      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      // Data should still be consistent
      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toHaveTextContent('750');
      });
    });

    it('should populate donation head selector with fetched data', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-selector')).toBeInTheDocument();
      });

      const selector = screen.getByTestId('donation-head-selector');

      // Should have default "All" option plus fetched donation heads
      expect(within(selector).getByText('All Donation Heads')).toBeInTheDocument();
      expect(within(selector).getByText('Education Fund')).toBeInTheDocument();
      expect(within(selector).getByText('Healthcare Support')).toBeInTheDocument();
      expect(within(selector).getByText('Food Aid Program')).toBeInTheDocument();
    });
  });

  describe('Error Handling and Edge Cases', () => {


    it('should handle empty data responses', async () => {
      mockedAxios.get.mockImplementation((url) => {
        if (url === '/api/tenant/dashboard') {
          return Promise.resolve({ data: mockTenantData });
        }
        return Promise.resolve({ data: [] });
      });

      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('no-donors-message')).toBeInTheDocument();
        expect(screen.getByTestId('no-activities-message')).toBeInTheDocument();
        expect(screen.getByTestId('no-heads-message')).toBeInTheDocument();
      });

      // Statistics should still show
      expect(screen.getByTestId('total-donations-value')).toHaveTextContent('750');
    });


  });

  describe('Performance and Optimization', () => {
    it('should not make unnecessary API calls when only UI state changes', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('create-donation-head-button')).toBeInTheDocument();
      });

      // Clear API call history
      mockedAxios.get.mockClear();

      // Click action buttons (should not trigger API calls)
      const createButton = screen.getByTestId('create-donation-head-button');
      await user.click(createButton);

      const manageButton = screen.getByTestId('manage-donors-button');
      await user.click(manageButton);

      // Should not have made any new API calls
      expect(mockedAxios.get).not.toHaveBeenCalled();
    });

    it('should debounce rapid filter changes', async () => {
      renderWithProviders(<IntegratedDonationTenantPage />);

      await waitFor(() => {
        expect(screen.getByTestId('period-selector')).toBeInTheDocument();
      });

      // Clear API call history
      mockedAxios.get.mockClear();

      // Rapidly change period multiple times
      const periodSelector = screen.getByTestId('period-selector');
      await user.selectOptions(periodSelector, 'this-week');
      await user.selectOptions(periodSelector, 'this-quarter');
      await user.selectOptions(periodSelector, 'this-year');

      // Should eventually make the API call with the final value
      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith('/api/tenant/charts?period=this-year&head=all');
      });
    });
  });
});
