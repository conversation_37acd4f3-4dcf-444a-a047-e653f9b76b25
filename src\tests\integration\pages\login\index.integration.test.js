/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { useRouter } from 'next/router';
import { useAuth } from 'src/hooks/useAuth';
import { AuthContext } from 'src/context/AuthContext';
import LoginPage from 'src/pages/login/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock dependencies
jest.mock('axios');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('src/hooks/useAuth', () => ({
  useAuth: jest.fn(),
}), { virtual: true });

jest.mock('src/@core/components/custom-components/FetchIpAddress', () => ({
  fetchIpAddress: jest.fn(() => Promise.resolve('***********')),
}), { virtual: true });

const mockedAxios = axios;

describe('Login Integration Tests', () => {
  const mockPush = jest.fn();
  const mockLoginNew = jest.fn();
  const user = userEvent.setup();

  const mockAuthContext = {
    loginLoad: false,
  };

  const mockAuth = {
    loginNew: mockLoginNew,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue({
      push: mockPush,
    });
    useAuth.mockReturnValue(mockAuth);
    mockedAxios.post.mockClear();
  });

  const renderLoginPage = (authContextValue = mockAuthContext) => {
    return renderWithProviders(
      <AuthContext.Provider value={authContextValue}>
        <LoginPage />
      </AuthContext.Provider>
    );
  };

  describe('Complete Login Flow', () => {
    it('should complete successful login flow', async () => {
      // Mock successful login response
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        setTimeout(() => handleSuccess(), 100);
      });

      renderLoginPage();

      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Verify login was called with correct parameters
      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            password: 'password123',
            ipAddress: '***********',
            overrideExistingLogins: false,
          }),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function)
        );
      });

      // Verify success dialog appears
      await waitFor(() => {
        expect(screen.getByText('Login Successful.')).toBeInTheDocument();
        expect(screen.getByText('Redirecting to your dashboard.')).toBeInTheDocument();
      });
    });

    it('should handle login failure with error dialog', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure) => {
        setTimeout(() => handleFailure(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });

      // Close error dialog
      const okayButton = screen.getByRole('button', { name: 'Okay' });
      await user.click(okayButton);

      await waitFor(() => {
        expect(screen.queryByText('Email or password credentials are invalid.')).not.toBeInTheDocument();
      });
    });

    it('should handle multiple device login scenario', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess, handlePopup) => {
        setTimeout(() => handlePopup(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Verify multiple device dialog appears
      await waitFor(() => {
        expect(screen.getByText(/You are currently logged in on multiple devices/)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Yes' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'No' })).toBeInTheDocument();
      });

      // Click Yes to override existing logins
      const yesButton = screen.getByRole('button', { name: 'Yes' });
      await user.click(yesButton);

      // Verify second login call with override flag
      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledTimes(2);
        expect(mockLoginNew).toHaveBeenLastCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            password: 'password123',
            overrideExistingLogins: true,
          }),
          expect.any(Function),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });

    it('should handle multiple device login cancellation', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess, handlePopup) => {
        setTimeout(() => handlePopup(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'No' })).toBeInTheDocument();
      });

      // Click No to cancel
      const noButton = screen.getByRole('button', { name: 'No' });
      await user.click(noButton);

      await waitFor(() => {
        expect(screen.queryByText(/You are currently logged in on multiple devices/)).not.toBeInTheDocument();
      });

      // Should not make second login call
      expect(mockLoginNew).toHaveBeenCalledTimes(1);
    });
  });

  describe('Form Validation Integration', () => {
    it('should prevent submission with invalid email', async () => {
      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, 'invalid-email');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Enter a valid email address')).toBeInTheDocument();
      });

      // Should not call login
      expect(mockLoginNew).not.toHaveBeenCalled();
    });

    it('should prevent submission with empty fields', async () => {
      renderLoginPage();

      const loginButton = screen.getByRole('button', { name: /login/i });
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
        expect(screen.getByText('Password is required')).toBeInTheDocument();
      });

      expect(mockLoginNew).not.toHaveBeenCalled();
    });

    it('should allow submission with valid data', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        setTimeout(() => handleSuccess(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Clear any validation errors
      await waitFor(() => {
        expect(screen.queryByText('Enter a valid email address')).not.toBeInTheDocument();
        expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
        expect(screen.queryByText('Password is required')).not.toBeInTheDocument();
      });

      await user.click(loginButton);

      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalled();
      });
    });
  });

  describe('Navigation Integration', () => {
    it('should navigate to registration with role selection', async () => {
      renderLoginPage();

      const createAccountButton = screen.getByText('Create Account');
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      const ngoOption = screen.getByText('Sign Up as NGO');
      await user.click(ngoOption);

      expect(mockPush).toHaveBeenCalledWith('/register?role=ngo');
    });

    it('should navigate to forgot password', async () => {
      renderLoginPage();

      const forgotPasswordLink = screen.getByText('Forgot Password ?');
      await user.click(forgotPasswordLink);

      expect(mockPush).toHaveBeenCalledWith('/forgot-password');
    });
  });

  describe('Loading States Integration', () => {
    it('should show loading spinner when loginLoad is true', () => {
      renderLoginPage({ loginLoad: true });

      expect(screen.getByTestId('fallback-spinner')).toBeInTheDocument();
      expect(screen.queryByText('Log In')).not.toBeInTheDocument();
    });

    it('should hide loading spinner when loginLoad is false', () => {
      renderLoginPage({ loginLoad: false });

      expect(screen.queryByTestId('fallback-spinner')).not.toBeInTheDocument();
      expect(screen.getByText('Log In')).toBeInTheDocument();
    });

    it('should show loading in success dialog', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        setTimeout(() => handleSuccess(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Login Successful.')).toBeInTheDocument();
      });

      // Should show loading spinner in success dialog
      const loadingSpinner = screen.getByRole('progressbar');
      expect(loadingSpinner).toBeInTheDocument();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors gracefully', async () => {
      mockLoginNew.mockRejectedValue(new Error('Network error'));

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });
    });

    it('should handle IP address fetch errors', async () => {
      const { fetchIpAddress } = require('src/@core/components/custom-components/FetchIpAddress');
      fetchIpAddress.mockRejectedValue(new Error('IP fetch failed'));

      mockLoginNew.mockImplementation((fields, handleFailure) => {
        setTimeout(() => handleFailure(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });
    });
  });

  describe('UI Interaction Integration', () => {
    it('should toggle password visibility and maintain form state', async () => {
      renderLoginPage();

      const passwordInput = screen.getByLabelText(/password/i);
      const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon

      await user.type(passwordInput, 'mypassword');
      expect(passwordInput).toHaveValue('mypassword');
      expect(passwordInput).toHaveAttribute('type', 'password');

      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
      expect(passwordInput).toHaveValue('mypassword'); // Value should be preserved

      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(passwordInput).toHaveValue('mypassword'); // Value should still be preserved
    });

    it('should maintain form state during dialog interactions', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure) => {
        setTimeout(() => handleFailure(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });

      // Close dialog
      const okayButton = screen.getByRole('button', { name: 'Okay' });
      await user.click(okayButton);

      // Form values should be preserved
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(passwordInput).toHaveValue('password123');
    });
  });

  describe('Accessibility Integration', () => {
    it('should support keyboard navigation through form', async () => {
      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      // Tab through form elements
      emailInput.focus();
      expect(emailInput).toHaveFocus();

      await user.tab();
      expect(passwordInput).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: '' })).toHaveFocus(); // Password toggle

      await user.tab();
      expect(screen.getByText('Forgot Password ?')).toHaveFocus();

      await user.tab();
      expect(screen.getByText('Create Account')).toHaveFocus();

      await user.tab();
      expect(loginButton).toHaveFocus();
    });

    it('should support form submission via Enter key', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        setTimeout(() => handleSuccess(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      
      // Press Enter to submit
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalled();
      });
    });

    it('should have proper ARIA labels and roles', () => {
      renderLoginPage();

      // Check form accessibility
      expect(screen.getByLabelText(/email/i)).toHaveAttribute('type', 'email');
      expect(screen.getByLabelText(/password/i)).toHaveAttribute('type', 'password');
      
      // Check button roles
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument();
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle rapid form submissions', async () => {
      let callCount = 0;
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        callCount++;
        setTimeout(() => handleSuccess(), 200);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // Rapidly click login button multiple times
      await user.click(loginButton);
      await user.click(loginButton);
      await user.click(loginButton);

      // Should handle multiple calls gracefully
      await waitFor(() => {
        expect(callCount).toBeGreaterThan(0);
      });
    });

    it('should handle form state after successful login', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        setTimeout(() => handleSuccess(), 100);
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Login Successful.')).toBeInTheDocument();
      });

      // Form should maintain values during success state
      expect(emailInput).toHaveValue('<EMAIL>');
      expect(passwordInput).toHaveValue('password123');
    });
  });
});
