/**
 * API Tests for Login Authentication
 * Tests all login-related API endpoints and authentication flows
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Login API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const loginEndpoint = `${baseURL}/auth/login`;

  // Mock data
  const mockLoginRequest = {
    email: '<EMAIL>',
    password: 'password123',
    ipAddress: '***********',
    overrideExistingLogins: false,
  };

  const mockLoginResponse = {
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'donor',
        isActive: true,
        lastLogin: '2024-01-15T10:00:00Z',
      },
      tokens: {
        accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        refreshToken: 'refresh_token_here',
        expiresIn: 3600,
      },
    },
  };

  const mockMultipleDeviceResponse = {
    success: false,
    code: 'MULTIPLE_DEVICES',
    message: 'User is logged in on multiple devices',
    data: {
      activeDevices: 3,
      lastLoginDevice: 'Chrome on Windows',
      requiresOverride: true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockLoginResponse,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(loginEndpoint, mockLoginRequest);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockLoginResponse);
      expect(response.data.success).toBe(true);
      expect(response.data.data.user.email).toBe('<EMAIL>');
      expect(response.data.data.tokens.accessToken).toBeDefined();
    });

    it('should handle invalid credentials', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Invalid email or password',
            code: 'INVALID_CREDENTIALS',
          }
        }
      });

      try {
        await axios.post(loginEndpoint, {
          ...mockLoginRequest,
          password: 'wrongpassword',
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        expect(error.response.data.message).toBe('Invalid email or password');
        expect(error.response.data.code).toBe('INVALID_CREDENTIALS');
      }
    });

    it('should handle multiple device login scenario', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: mockMultipleDeviceResponse,
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.code).toBe('MULTIPLE_DEVICES');
        expect(error.response.data.data.requiresOverride).toBe(true);
        expect(error.response.data.data.activeDevices).toBe(3);
      }
    });

    it('should handle override existing logins', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLoginResponse,
          message: 'Login successful. Previous sessions terminated.',
        },
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const overrideRequest = {
        ...mockLoginRequest,
        overrideExistingLogins: true,
      };

      const response = await axios.post(loginEndpoint, overrideRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(loginEndpoint, overrideRequest);
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.message).toContain('Previous sessions terminated');
    });

    it('should handle account locked scenario', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 423,
          data: {
            success: false,
            message: 'Account is temporarily locked due to multiple failed login attempts',
            code: 'ACCOUNT_LOCKED',
            data: {
              lockoutTime: '2024-01-15T11:00:00Z',
              remainingTime: 1800, // 30 minutes in seconds
            },
          }
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(423);
        expect(error.response.data.code).toBe('ACCOUNT_LOCKED');
        expect(error.response.data.data.remainingTime).toBe(1800);
      }
    });

    it('should handle inactive account', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 403,
          data: {
            success: false,
            message: 'Account is inactive. Please contact support.',
            code: 'ACCOUNT_INACTIVE',
          }
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.code).toBe('ACCOUNT_INACTIVE');
      }
    });

    it('should handle email not verified', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 403,
          data: {
            success: false,
            message: 'Email not verified. Please check your email.',
            code: 'EMAIL_NOT_VERIFIED',
            data: {
              email: '<EMAIL>',
              canResendVerification: true,
            },
          }
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.code).toBe('EMAIL_NOT_VERIFIED');
        expect(error.response.data.data.canResendVerification).toBe(true);
      }
    });

    it('should validate required fields', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'email', message: 'Email is required' },
              { field: 'password', message: 'Password is required' },
            ],
          }
        }
      });

      try {
        await axios.post(loginEndpoint, {
          email: '',
          password: '',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
        expect(error.response.data.errors).toHaveLength(2);
      }
    });

    it('should validate email format', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid email format',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'email', message: 'Please enter a valid email address' },
            ],
          }
        }
      });

      try {
        await axios.post(loginEndpoint, {
          ...mockLoginRequest,
          email: 'invalid-email',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
      }
    });

    it('should handle rate limiting', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 429,
          data: {
            success: false,
            message: 'Too many login attempts. Please try again later.',
            code: 'RATE_LIMITED',
            data: {
              retryAfter: 300, // 5 minutes
              maxAttempts: 5,
              currentAttempts: 5,
            },
          },
          headers: {
            'retry-after': '300',
          },
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data.code).toBe('RATE_LIMITED');
        expect(error.response.headers['retry-after']).toBe('300');
      }
    });

    it('should handle server errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error',
            code: 'INTERNAL_ERROR',
          }
        }
      });

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data.code).toBe('INTERNAL_ERROR');
      }
    });

    it('should handle network errors', async () => {
      mockedAxios.post.mockRejectedValue(new Error('Network Error'));

      try {
        await axios.post(loginEndpoint, mockLoginRequest);
      } catch (error) {
        expect(error.message).toBe('Network Error');
      }
    });
  });

  describe('Login with Different User Roles', () => {
    it('should login donor successfully', async () => {
      const donorResponse = {
        ...mockLoginResponse,
        data: {
          ...mockLoginResponse.data,
          user: {
            ...mockLoginResponse.data.user,
            role: 'donor',
            permissions: ['donate', 'view_receipts'],
          },
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: donorResponse,
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.data.data.user.role).toBe('donor');
      expect(response.data.data.user.permissions).toContain('donate');
    });

    it('should login NGO successfully', async () => {
      const ngoResponse = {
        ...mockLoginResponse,
        data: {
          ...mockLoginResponse.data,
          user: {
            ...mockLoginResponse.data.user,
            role: 'ngo',
            organizationId: 'org-123',
            permissions: ['manage_donations', 'view_reports'],
          },
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: ngoResponse,
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.data.data.user.role).toBe('ngo');
      expect(response.data.data.user.organizationId).toBe('org-123');
      expect(response.data.data.user.permissions).toContain('manage_donations');
    });

    it('should login admin successfully', async () => {
      const adminResponse = {
        ...mockLoginResponse,
        data: {
          ...mockLoginResponse.data,
          user: {
            ...mockLoginResponse.data.user,
            role: 'admin',
            permissions: ['admin_access', 'manage_users', 'view_analytics'],
          },
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: adminResponse,
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.data.data.user.role).toBe('admin');
      expect(response.data.data.user.permissions).toContain('admin_access');
    });
  });

  describe('Security Features', () => {
    it('should include security headers in response', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockLoginResponse,
        status: 200,
        headers: { 
          'content-type': 'application/json',
          'x-frame-options': 'DENY',
          'x-content-type-options': 'nosniff',
          'strict-transport-security': 'max-age=31536000',
        }
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['strict-transport-security']).toBe('max-age=31536000');
    });

    it('should handle IP address tracking', async () => {
      const requestWithIP = {
        ...mockLoginRequest,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      };

      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLoginResponse,
          data: {
            ...mockLoginResponse.data,
            loginInfo: {
              ipAddress: '***********',
              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              loginTime: '2024-01-15T10:00:00Z',
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(loginEndpoint, requestWithIP);

      expect(response.data.data.loginInfo.ipAddress).toBe('***********');
      expect(response.data.data.loginInfo.userAgent).toContain('Mozilla');
    });

    it('should handle suspicious login detection', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 403,
          data: {
            success: false,
            message: 'Suspicious login detected. Additional verification required.',
            code: 'SUSPICIOUS_LOGIN',
            data: {
              requiresTwoFactor: true,
              verificationMethods: ['email', 'sms'],
            },
          }
        }
      });

      try {
        await axios.post(loginEndpoint, {
          ...mockLoginRequest,
          ipAddress: '*******', // Different country IP
        });
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.code).toBe('SUSPICIOUS_LOGIN');
        expect(error.response.data.data.requiresTwoFactor).toBe(true);
      }
    });
  });

  describe('Token Management', () => {
    it('should return valid JWT tokens', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockLoginResponse,
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      const { accessToken, refreshToken, expiresIn } = response.data.data.tokens;
      
      expect(accessToken).toBeDefined();
      expect(refreshToken).toBeDefined();
      expect(expiresIn).toBe(3600);
      expect(typeof accessToken).toBe('string');
      expect(typeof refreshToken).toBe('string');
    });

    it('should handle token refresh scenario', async () => {
      const refreshResponse = {
        success: true,
        message: 'Token refreshed successfully',
        data: {
          tokens: {
            accessToken: 'new_access_token_here',
            refreshToken: 'new_refresh_token_here',
            expiresIn: 3600,
          },
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: refreshResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/refresh`, {
        refreshToken: 'old_refresh_token',
      });

      expect(response.data.data.tokens.accessToken).toBe('new_access_token_here');
      expect(response.data.data.tokens.refreshToken).toBe('new_refresh_token_here');
    });
  });

  describe('Login Analytics', () => {
    it('should track login attempts', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLoginResponse,
          data: {
            ...mockLoginResponse.data,
            analytics: {
              totalLogins: 15,
              lastLoginDate: '2024-01-14T09:30:00Z',
              loginStreak: 5,
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.data.data.analytics.totalLogins).toBe(15);
      expect(response.data.data.analytics.loginStreak).toBe(5);
    });

    it('should handle first-time login', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLoginResponse,
          data: {
            ...mockLoginResponse.data,
            isFirstLogin: true,
            analytics: {
              totalLogins: 1,
              lastLoginDate: null,
              loginStreak: 1,
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(loginEndpoint, mockLoginRequest);

      expect(response.data.data.isFirstLogin).toBe(true);
      expect(response.data.data.analytics.totalLogins).toBe(1);
    });
  });
});
