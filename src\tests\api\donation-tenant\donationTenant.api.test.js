/**
 * API Tests for Donation Tenant Dashboard
 * Tests all tenant-specific endpoints including dashboard data, charts, and tenant management
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Donation Tenant API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const tenantEndpoint = `${baseURL}/tenant`;

  // Mock data
  const mockTenantData = {
    totalDonations: 750,
    totalAmount: 37500,
    activeDonationHeads: 15,
    averageDonation: 50,
    growthRate: 12.5,
    topDonor: '<PERSON>',
    thisMonthDonations: 125,
    thisMonthAmount: 6250,
    organizationName: 'Education Foundation',
    organizationId: 'org-1',
  };

  const mockChartData = {
    series: [
      { name: 'Donations', data: [50, 75, 100, 90, 110, 125] },
      { name: 'Amount', data: [2500, 3750, 5000, 4500, 5500, 6250] },
    ],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    colors: ['#1976d2', '#dc004e'],
    period: 'monthly',
  };

  const mockTopDonors = [
    {
      id: '1',
      name: 'Alice Johnson',
      totalAmount: 1500,
      donationCount: 15,
      lastDonation: '2024-01-15',
      averageDonation: 100,
    },
    {
      id: '2',
      name: 'Bob Wilson',
      totalAmount: 1200,
      donationCount: 12,
      lastDonation: '2024-01-14',
      averageDonation: 100,
    },
    {
      id: '3',
      name: 'Carol Davis',
      totalAmount: 1000,
      donationCount: 10,
      lastDonation: '2024-01-13',
      averageDonation: 100,
    },
  ];

  const mockRecentActivities = [
    {
      id: '1',
      type: 'Donation',
      description: 'New donation received from Alice Johnson',
      date: '2024-01-15',
      user: 'Alice Johnson',
      amount: 100,
      donationHeadId: 'head-1',
    },
    {
      id: '2',
      type: 'Donation Head',
      description: 'Education Fund donation head created',
      date: '2024-01-14',
      user: 'Admin User',
      donationHeadId: 'head-1',
    },
    {
      id: '3',
      type: 'Receipt',
      description: 'Receipt generated for donation #123',
      date: '2024-01-13',
      user: 'System',
      receiptNumber: 'RCP-123',
    },
  ];

  const mockDonationHeads = [
    {
      id: '1',
      name: 'Education Fund',
      totalDonations: 100,
      totalAmount: 10000,
      isActive: true,
      lastDonation: '2024-01-15',
      averageDonation: 100,
    },
    {
      id: '2',
      name: 'Healthcare Support',
      totalDonations: 150,
      totalAmount: 15000,
      isActive: true,
      lastDonation: '2024-01-14',
      averageDonation: 100,
    },
    {
      id: '3',
      name: 'Food Aid Program',
      totalDonations: 75,
      totalAmount: 7500,
      isActive: false,
      lastDonation: '2024-01-10',
      averageDonation: 100,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/tenant/dashboard', () => {
    it('should fetch tenant dashboard data successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockTenantData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/dashboard`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/dashboard`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockTenantData);
      expect(typeof response.data.totalDonations).toBe('number');
      expect(typeof response.data.totalAmount).toBe('number');
      expect(typeof response.data.growthRate).toBe('number');
    });

    it('should handle dashboard data with organization filter', async () => {
      const orgFilteredData = { ...mockTenantData, organizationId: 'org-2' };
      mockedAxios.get.mockResolvedValue({ 
        data: orgFilteredData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/dashboard?orgId=org-2`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/dashboard?orgId=org-2`);
      expect(response.status).toBe(200);
      expect(response.data.organizationId).toBe('org-2');
    });

    it('should handle dashboard data with date range', async () => {
      const dateFilteredData = { ...mockTenantData, totalDonations: 300 };
      mockedAxios.get.mockResolvedValue({ 
        data: dateFilteredData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/dashboard?startDate=2024-01-01&endDate=2024-01-31`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/dashboard?startDate=2024-01-01&endDate=2024-01-31`);
      expect(response.status).toBe(200);
      expect(response.data.totalDonations).toBe(300);
    });

    it('should handle tenant access restrictions', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Access denied for this organization' }
        }
      });

      try {
        await axios.get(`${tenantEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Access denied for this organization');
      }
    });
  });

  describe('GET /api/tenant/charts', () => {
    it('should fetch tenant chart data successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockChartData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/charts`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/charts`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockChartData);
      expect(Array.isArray(response.data.series)).toBe(true);
      expect(Array.isArray(response.data.categories)).toBe(true);
    });

    it('should handle chart data with period and donation head filters', async () => {
      const filteredChartData = { ...mockChartData, period: 'weekly', donationHeadId: 'head-1' };
      mockedAxios.get.mockResolvedValue({ 
        data: filteredChartData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/charts?period=weekly&head=head-1`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/charts?period=weekly&head=head-1`);
      expect(response.status).toBe(200);
      expect(response.data.period).toBe('weekly');
      expect(response.data.donationHeadId).toBe('head-1');
    });

    it('should handle chart data with custom date range', async () => {
      const customRangeData = { ...mockChartData, customRange: true };
      mockedAxios.get.mockResolvedValue({ 
        data: customRangeData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/charts?startDate=2024-01-01&endDate=2024-06-30`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/charts?startDate=2024-01-01&endDate=2024-06-30`);
      expect(response.status).toBe(200);
      expect(response.data.customRange).toBe(true);
    });
  });

  describe('GET /api/tenant/top-donors', () => {
    it('should fetch top donors successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockTopDonors,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/top-donors`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/top-donors`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockTopDonors);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(3);
    });

    it('should handle top donors with limit parameter', async () => {
      const limitedDonors = mockTopDonors.slice(0, 2);
      mockedAxios.get.mockResolvedValue({ 
        data: limitedDonors,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/top-donors?limit=2`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/top-donors?limit=2`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(2);
    });

    it('should handle top donors with donation head filter', async () => {
      const filteredDonors = [mockTopDonors[0]];
      mockedAxios.get.mockResolvedValue({ 
        data: filteredDonors,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/top-donors?donationHead=head-1`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/top-donors?donationHead=head-1`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(1);
    });

    it('should handle top donors with time period filter', async () => {
      const periodDonors = mockTopDonors.slice(0, 1);
      mockedAxios.get.mockResolvedValue({ 
        data: periodDonors,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/top-donors?period=this-month`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/top-donors?period=this-month`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(1);
    });
  });

  describe('GET /api/tenant/recent-activities', () => {
    it('should fetch recent activities successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockRecentActivities,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/recent-activities`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/recent-activities`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockRecentActivities);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(3);
    });

    it('should handle recent activities with type filter', async () => {
      const donationActivities = mockRecentActivities.filter(activity => activity.type === 'Donation');
      mockedAxios.get.mockResolvedValue({ 
        data: donationActivities,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/recent-activities?type=Donation`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/recent-activities?type=Donation`);
      expect(response.status).toBe(200);
      expect(response.data.every(activity => activity.type === 'Donation')).toBe(true);
    });

    it('should handle recent activities with limit', async () => {
      const limitedActivities = mockRecentActivities.slice(0, 2);
      mockedAxios.get.mockResolvedValue({ 
        data: limitedActivities,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/recent-activities?limit=2`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/recent-activities?limit=2`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(2);
    });
  });

  describe('GET /api/tenant/donation-heads', () => {
    it('should fetch tenant donation heads successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockDonationHeads,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/donation-heads`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/donation-heads`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockDonationHeads);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(3);
    });

    it('should handle donation heads with active filter', async () => {
      const activeHeads = mockDonationHeads.filter(head => head.isActive);
      mockedAxios.get.mockResolvedValue({ 
        data: activeHeads,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/donation-heads?isActive=true`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/donation-heads?isActive=true`);
      expect(response.status).toBe(200);
      expect(response.data.every(head => head.isActive)).toBe(true);
    });

    it('should handle donation heads with sorting', async () => {
      const sortedHeads = [...mockDonationHeads].sort((a, b) => b.totalAmount - a.totalAmount);
      mockedAxios.get.mockResolvedValue({ 
        data: sortedHeads,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${tenantEndpoint}/donation-heads?sortBy=totalAmount&order=desc`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${tenantEndpoint}/donation-heads?sortBy=totalAmount&order=desc`);
      expect(response.status).toBe(200);
      expect(response.data[0].totalAmount).toBeGreaterThanOrEqual(response.data[1].totalAmount);
    });
  });

  describe('POST /api/tenant/export', () => {
    it('should export tenant data as CSV successfully', async () => {
      const csvData = 'Donor,Amount,Date,Donation Head\nAlice Johnson,100,2024-01-15,Education Fund';
      mockedAxios.post.mockResolvedValue({ 
        data: csvData,
        status: 200,
        headers: { 
          'content-type': 'text/csv',
          'content-disposition': 'attachment; filename="tenant-report.csv"'
        }
      });

      const exportRequest = {
        format: 'csv',
        period: 'this-month',
        donationHead: 'all',
        includeFields: ['donor', 'amount', 'date', 'donationHead']
      };

      const response = await axios.post(`${tenantEndpoint}/export`, exportRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${tenantEndpoint}/export`, exportRequest);
      expect(response.status).toBe(200);
      expect(response.data).toBe(csvData);
      expect(response.headers['content-type']).toBe('text/csv');
    });

    it('should export tenant data as PDF successfully', async () => {
      const pdfData = 'mock-pdf-binary-data';
      mockedAxios.post.mockResolvedValue({ 
        data: pdfData,
        status: 200,
        headers: { 
          'content-type': 'application/pdf',
          'content-disposition': 'attachment; filename="tenant-report.pdf"'
        }
      });

      const exportRequest = {
        format: 'pdf',
        period: 'this-quarter',
        donationHead: 'head-1',
        includeCharts: true,
        includeSummary: true
      };

      const response = await axios.post(`${tenantEndpoint}/export`, exportRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${tenantEndpoint}/export`, exportRequest);
      expect(response.status).toBe(200);
      expect(response.data).toBe(pdfData);
      expect(response.headers['content-type']).toBe('application/pdf');
    });

    it('should handle export with specific donation head filter', async () => {
      const filteredCsvData = 'Donor,Amount,Date\nAlice Johnson,100,2024-01-15';
      mockedAxios.post.mockResolvedValue({ 
        data: filteredCsvData,
        status: 200,
        headers: { 'content-type': 'text/csv' }
      });

      const exportRequest = {
        format: 'csv',
        period: 'this-year',
        donationHead: 'head-1'
      };

      const response = await axios.post(`${tenantEndpoint}/export`, exportRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${tenantEndpoint}/export`, exportRequest);
      expect(response.status).toBe(200);
      expect(response.data).toBe(filteredCsvData);
    });

    it('should handle export validation errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { 
            error: 'Invalid export parameters',
            details: ['Format must be csv or pdf', 'Period is required']
          }
        }
      });

      try {
        await axios.post(`${tenantEndpoint}/export`, { format: 'invalid' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Invalid export parameters');
        expect(Array.isArray(error.response.data.details)).toBe(true);
      }
    });
  });

  describe('POST /api/tenant/donation-heads', () => {
    const newDonationHead = {
      name: 'Emergency Relief Fund',
      description: 'Emergency assistance program',
      isActive: true,
    };

    it('should create a new donation head successfully', async () => {
      const createdHead = {
        id: '4',
        ...newDonationHead,
        orgId: 'org-1',
        totalDonations: 0,
        totalAmount: 0,
        createdOn: new Date().toISOString(),
        updatedOn: new Date().toISOString(),
      };

      mockedAxios.post.mockResolvedValue({ 
        data: createdHead,
        status: 201,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(`${tenantEndpoint}/donation-heads`, newDonationHead);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${tenantEndpoint}/donation-heads`, newDonationHead);
      expect(response.status).toBe(201);
      expect(response.data).toEqual(createdHead);
      expect(response.data.id).toBeDefined();
      expect(response.data.name).toBe(newDonationHead.name);
    });

    it('should handle donation head creation validation errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { 
            error: 'Validation failed',
            details: ['Name is required', 'Name must be unique within organization']
          }
        }
      });

      try {
        await axios.post(`${tenantEndpoint}/donation-heads`, { name: '' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Validation failed');
        expect(Array.isArray(error.response.data.details)).toBe(true);
      }
    });

    it('should handle organization limits', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Maximum number of donation heads reached for this organization' }
        }
      });

      try {
        await axios.post(`${tenantEndpoint}/donation-heads`, newDonationHead);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Maximum number of donation heads reached for this organization');
      }
    });
  });

  describe('PUT /api/tenant/donation-heads/:id', () => {
    const updateData = {
      name: 'Updated Emergency Relief Fund',
      description: 'Updated emergency assistance program',
      isActive: false,
    };

    it('should update a donation head successfully', async () => {
      const updatedHead = {
        ...mockDonationHeads[0],
        ...updateData,
        updatedOn: new Date().toISOString(),
      };

      mockedAxios.put.mockResolvedValue({ 
        data: updatedHead,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.put(`${tenantEndpoint}/donation-heads/1`, updateData);

      expect(mockedAxios.put).toHaveBeenCalledWith(`${tenantEndpoint}/donation-heads/1`, updateData);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(updatedHead);
      expect(response.data.name).toBe(updateData.name);
    });

    it('should handle update authorization errors', async () => {
      mockedAxios.put.mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Not authorized to update this donation head' }
        }
      });

      try {
        await axios.put(`${tenantEndpoint}/donation-heads/999`, updateData);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Not authorized to update this donation head');
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle tenant not found errors', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 404,
          data: { error: 'Tenant organization not found' }
        }
      });

      try {
        await axios.get(`${tenantEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.error).toBe('Tenant organization not found');
      }
    });

    it('should handle inactive tenant errors', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Tenant organization is inactive' }
        }
      });

      try {
        await axios.get(`${tenantEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Tenant organization is inactive');
      }
    });

    it('should handle data privacy restrictions', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 451,
          data: { error: 'Data access restricted due to privacy regulations' }
        }
      });

      try {
        await axios.get(`${tenantEndpoint}/top-donors`);
      } catch (error) {
        expect(error.response.status).toBe(451);
        expect(error.response.data.error).toBe('Data access restricted due to privacy regulations');
      }
    });

    it('should handle quota exceeded errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 429,
          data: { 
            error: 'API quota exceeded',
            resetTime: '2024-01-16T00:00:00Z'
          }
        }
      });

      try {
        await axios.post(`${tenantEndpoint}/export`, { format: 'csv' });
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data.error).toBe('API quota exceeded');
        expect(error.response.data.resetTime).toBeDefined();
      }
    });
  });
});
