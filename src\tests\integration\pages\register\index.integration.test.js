/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { useRouter } from 'next/router';
import { AuthContext } from 'src/context/AuthContext';
import RegisterPage from 'src/pages/register/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock dependencies
jest.mock('axios');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('src/@core/components/custom-components/FetchIpAddress', () => ({
  fetchIpAddress: jest.fn(() => Promise.resolve('***********')),
}), { virtual: true });

const mockedAxios = axios;

describe('Register Integration Tests', () => {
  const mockPush = jest.fn();
  const mockHandleRegister = jest.fn();
  const user = userEvent.setup();

  const mockAuthContext = {
    handleRegister: mockHandleRegister,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue({
      push: mockPush,
      query: { role: 'donor' },
    });
    mockedAxios.post.mockClear();
    mockedAxios.get.mockClear();
  });

  const renderRegisterPage = (authContextValue = mockAuthContext, routerQuery = { role: 'donor' }) => {
    useRouter.mockReturnValue({
      push: mockPush,
      query: routerQuery,
    });

    return renderWithProviders(
      <AuthContext.Provider value={authContextValue}>
        <RegisterPage />
      </AuthContext.Provider>
    );
  };

  describe('Complete Registration Flow - Donor', () => {
    it('should complete successful donor registration flow', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback, successCallback) => {
        setTimeout(() => successCallback({ message: 'Registration successful' }), 100);
      });

      renderRegisterPage();

      // Fill in all required fields
      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/password/i), 'password123');

      // Accept terms and conditions
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      // Submit form
      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      // Verify registration was called with correct data
      await waitFor(() => {
        expect(mockHandleRegister).toHaveBeenCalledWith(
          expect.objectContaining({
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            mobileNumber: '**********',
            password: 'password123',
            role: 'donor',
            termsAccepted: true,
          }),
          expect.any(Function),
          expect.any(Function)
        );
      });

      // Verify success message
      await waitFor(() => {
        expect(screen.getByText('Registration successful')).toBeInTheDocument();
      });
    });

    it('should handle donor registration with email verification', async () => {
      // Mock OTP verification
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, message: 'OTP sent successfully' }
      });

      renderRegisterPage();

      // Fill email and trigger verification
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      // Verify OTP dialog opens
      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
        expect(screen.getByText(/OTP has been <NAME_EMAIL>/)).toBeInTheDocument();
      });

      // Enter OTP
      const otpInput = screen.getByLabelText('OTP');
      await user.type(otpInput, '123456');

      // Validate OTP
      const validateButton = screen.getByRole('button', { name: /validate/i });
      await user.click(validateButton);

      // Verify API call was made
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('verify'),
        expect.objectContaining({
          email: '<EMAIL>',
          otp: '123456',
        })
      );
    });

    it('should handle mobile number verification flow', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, message: 'OTP sent to mobile' }
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      
      const verifyMobileButton = screen.getByRole('button', { name: /verify mobile/i });
      await user.click(verifyMobileButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
        expect(screen.getByText(/OTP has been sent to **********/)).toBeInTheDocument();
      });

      const otpInput = screen.getByLabelText('OTP');
      await user.type(otpInput, '654321');

      const validateButton = screen.getByRole('button', { name: /validate/i });
      await user.click(validateButton);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('verify'),
        expect.objectContaining({
          mobileNumber: '**********',
          otp: '654321',
        })
      );
    });
  });

  describe('Complete Registration Flow - NGO', () => {
    it('should complete successful NGO registration flow', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback, successCallback) => {
        setTimeout(() => successCallback({ message: 'NGO registration successful' }), 100);
      });

      renderRegisterPage(mockAuthContext, { role: 'ngo' });

      // Fill in all required fields including NGO-specific ones
      await user.type(screen.getByLabelText(/first name/i), 'Jane');
      await user.type(screen.getByLabelText(/last name/i), 'Smith');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      await user.type(screen.getByLabelText(/organization name/i), 'Help Foundation');
      
      // Select organization type
      const orgTypeSelect = screen.getByLabelText(/organization type/i);
      await user.click(orgTypeSelect);
      await user.click(screen.getByText('Non-Profit'));

      // Accept terms
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      // Submit form
      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockHandleRegister).toHaveBeenCalledWith(
          expect.objectContaining({
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            organizationName: 'Help Foundation',
            organizationType: 'Non-Profit',
            role: 'ngo',
          }),
          expect.any(Function),
          expect.any(Function)
        );
      });

      await waitFor(() => {
        expect(screen.getByText('NGO registration successful')).toBeInTheDocument();
      });
    });

    it('should handle NGO document upload flow', async () => {
      renderRegisterPage(mockAuthContext, { role: 'ngo' });

      // Mock file upload
      const file = new File(['certificate'], 'certificate.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByLabelText(/upload certificate/i);
      
      await user.upload(fileInput, file);

      expect(fileInput.files[0]).toBe(file);
      expect(fileInput.files).toHaveLength(1);
    });
  });

  describe('Form Validation Integration', () => {
    it('should prevent submission with validation errors', async () => {
      renderRegisterPage();

      // Try to submit empty form
      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText('First name is required')).toBeInTheDocument();
        expect(screen.getByText('Last name is required')).toBeInTheDocument();
        expect(screen.getByText('Email is required')).toBeInTheDocument();
        expect(screen.getByText('Mobile number is required')).toBeInTheDocument();
        expect(screen.getByText('Password is required')).toBeInTheDocument();
        expect(screen.getByText('You must accept the terms and conditions')).toBeInTheDocument();
      });

      // Should not call registration
      expect(mockHandleRegister).not.toHaveBeenCalled();
    });

    it('should validate email format in real-time', async () => {
      renderRegisterPage();

      const emailInput = screen.getByLabelText(/email/i);
      
      // Type invalid email
      await user.type(emailInput, 'invalid-email');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Enter a valid email address')).toBeInTheDocument();
      });

      // Clear and type valid email
      await user.clear(emailInput);
      await user.type(emailInput, '<EMAIL>');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText('Enter a valid email address')).not.toBeInTheDocument();
      });
    });

    it('should validate password strength', async () => {
      renderRegisterPage();

      const passwordInput = screen.getByLabelText(/password/i);
      
      // Type weak password
      await user.type(passwordInput, '123');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 8 characters')).toBeInTheDocument();
      });

      // Type strong password
      await user.clear(passwordInput);
      await user.type(passwordInput, 'strongPassword123');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText('Password must be at least 8 characters')).not.toBeInTheDocument();
      });
    });

    it('should validate mobile number format', async () => {
      renderRegisterPage();

      const mobileInput = screen.getByLabelText(/mobile number/i);
      
      // Type invalid mobile number
      await user.type(mobileInput, '123');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Mobile number must be 10 digits')).toBeInTheDocument();
      });

      // Type valid mobile number
      await user.clear(mobileInput);
      await user.type(mobileInput, '**********');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText('Mobile number must be 10 digits')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle registration failure with error message', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback) => {
        setTimeout(() => errorCallback('Email already exists'), 100);
      });

      renderRegisterPage();

      // Fill valid form
      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/password/i), 'password123');

      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email already exists')).toBeInTheDocument();
      });
    });

    it('should handle network errors gracefully', async () => {
      mockHandleRegister.mockRejectedValue(new Error('Network error'));

      renderRegisterPage();

      // Fill and submit form
      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/password/i), 'password123');

      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Registration failed. Please try again.')).toBeInTheDocument();
      });
    });

    it('should handle OTP verification errors', async () => {
      mockedAxios.post.mockRejectedValue(new Error('OTP verification failed'));

      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Failed to send OTP. Please try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation Integration', () => {
    it('should navigate to login page from registration', async () => {
      renderRegisterPage();

      const loginLink = screen.getByText('Sign in instead');
      await user.click(loginLink);

      expect(mockPush).toHaveBeenCalledWith('/login');
    });

    it('should handle role switching during registration', () => {
      // Start with donor registration
      const { rerender } = renderRegisterPage(mockAuthContext, { role: 'donor' });
      expect(screen.getByText('Donor Registration')).toBeInTheDocument();

      // Switch to NGO registration
      useRouter.mockReturnValue({
        push: mockPush,
        query: { role: 'ngo' },
      });

      rerender(
        <AuthContext.Provider value={mockAuthContext}>
          <RegisterPage />
        </AuthContext.Provider>
      );

      expect(screen.getByText('NGO Registration')).toBeInTheDocument();
      expect(screen.getByLabelText(/organization name/i)).toBeInTheDocument();
    });
  });

  describe('OTP Flow Integration', () => {
    it('should handle complete OTP verification flow', async () => {
      // Mock successful OTP send
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, message: 'OTP sent' }
      });

      // Mock successful OTP verification
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, message: 'OTP verified' }
      });

      renderRegisterPage();

      // Send OTP
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
      });

      // Enter and validate OTP
      const otpInput = screen.getByLabelText('OTP');
      await user.type(otpInput, '123456');

      const validateButton = screen.getByRole('button', { name: /validate/i });
      await user.click(validateButton);

      await waitFor(() => {
        expect(screen.getByText('Email verified successfully')).toBeInTheDocument();
      });

      // Dialog should close
      await waitFor(() => {
        expect(screen.queryByText('Verify OTP')).not.toBeInTheDocument();
      });
    });

    it('should handle OTP resend functionality', async () => {
      mockedAxios.post.mockResolvedValue({
        data: { success: true, message: 'OTP sent' }
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
      });

      // Click resend button
      const resendButton = screen.getByRole('button', { name: /resend/i });
      await user.click(resendButton);

      // Should make another API call
      expect(mockedAxios.post).toHaveBeenCalledTimes(2);
    });

    it('should handle OTP dialog close', async () => {
      mockedAxios.post.mockResolvedValue({
        data: { success: true, message: 'OTP sent' }
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
      });

      // Close dialog
      const closeButton = screen.getByRole('button', { name: '' }); // Close icon
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('Verify OTP')).not.toBeInTheDocument();
      });
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle form auto-save and recovery', async () => {
      renderRegisterPage();

      // Fill partial form
      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');

      // Simulate page refresh
      const { rerender } = renderRegisterPage();
      
      // Form should maintain state (if auto-save is implemented)
      // This would depend on actual implementation
      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    });

    it('should handle concurrent registrations', async () => {
      let callCount = 0;
      mockHandleRegister.mockImplementation((data, errorCallback, successCallback) => {
        callCount++;
        if (callCount === 1) {
          setTimeout(() => errorCallback('Server busy'), 100);
        } else {
          setTimeout(() => successCallback({ message: 'Registration successful' }), 100);
        }
      });

      renderRegisterPage();

      // Fill form
      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '**********');
      await user.type(screen.getByLabelText(/password/i), 'password123');

      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });

      // First attempt fails
      await user.click(submitButton);
      await waitFor(() => {
        expect(screen.getByText('Server busy')).toBeInTheDocument();
      });

      // Second attempt succeeds
      await user.click(submitButton);
      await waitFor(() => {
        expect(screen.getByText('Registration successful')).toBeInTheDocument();
      });
    });
  });
});
