import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import axios from 'axios';

// Create a test theme for integration tests
const integrationTestTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
  },
});

// Enhanced mock contexts for integration testing
const mockIntegrationAuthContext = {
  donationHeadDetails: null,
  user: {
    id: 'test-user-1',
    name: 'Integration Test User',
    role: 'admin',
    orgId: 'test-org-1',
    organisationCategory: 'SUPER_ADMIN',
    email: '<EMAIL>',
    permissions: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
  },
  isAuthenticated: true,
  token: 'mock-jwt-token',
  postDonationHead: jest.fn(),
  patchDonationHead: jest.fn(),
  deleteDonationHead: jest.fn(),
  refreshToken: jest.fn(),
  logout: jest.fn(),
};

const mockIntegrationRBACContext = {
  canAccessActions: jest.fn(() => true),
  canAccessHeads: jest.fn(() => true),
  hasPermission: jest.fn(() => true),
  canMenuPage: jest.fn(() => true),
  canMenuPageSection: jest.fn(() => true),
  rbacRoles: ['ADMIN', 'SUPER_ADMIN'],
  permissions: {
    'donation-head': ['CREATE', 'READ', 'UPDATE', 'DELETE'],
    'donation-admin': ['READ', 'EXPORT'],
    'donation-tenant': ['READ', 'EXPORT', 'MANAGE'],
  },
};

// Enhanced mock router for integration tests
const mockIntegrationRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  pathname: '/',
  query: {},
  asPath: '/',
  route: '/',
  basePath: '',
  locale: 'en',
  locales: ['en'],
  defaultLocale: 'en',
  isReady: true,
  isPreview: false,
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
};

// Integration test wrapper component
const IntegrationTestWrapper = ({ 
  children, 
  authContextValue = mockIntegrationAuthContext, 
  rbacContextValue = mockIntegrationRBACContext,
  routerValue = mockIntegrationRouter,
}) => {
  return (
    <ThemeProvider theme={integrationTestTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

// Enhanced render function for integration tests
export const renderWithIntegrationProviders = (component, options = {}) => {
  const { 
    authContextValue, 
    rbacContextValue, 
    routerValue,
    ...renderOptions 
  } = options;

  const Wrapper = ({ children }) => (
    <IntegrationTestWrapper
      authContextValue={authContextValue}
      rbacContextValue={rbacContextValue}
      routerValue={routerValue}
    >
      {children}
    </IntegrationTestWrapper>
  );

  return render(component, {
    wrapper: Wrapper,
    ...renderOptions,
  });
};

// API Mock Helpers for Integration Tests
export const createApiMockHelpers = () => {
  const mockAxios = axios;
  
  const setupSuccessfulApiMocks = (mockData = {}) => {
    const defaultMockData = {
      donationHeads: [
        {
          id: '1',
          name: 'Education Fund',
          description: 'Supporting education initiatives',
          orgId: 'test-org-1',
          isActive: true,
          createdOn: '2024-01-01T00:00:00Z',
          updatedOn: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Healthcare Support',
          description: 'Medical assistance program',
          orgId: 'test-org-1',
          isActive: false,
          createdOn: '2024-01-02T00:00:00Z',
          updatedOn: '2024-01-02T00:00:00Z',
        },
      ],
      dashboardData: {
        totalDonations: 1500,
        totalAmount: 75000,
        activeOrganizations: 30,
        growthRate: 12.5,
      },
      tenantData: {
        totalDonations: 750,
        totalAmount: 37500,
        activeDonationHeads: 15,
        averageDonation: 50,
        growthRate: 12.5,
      },
      chartData: {
        series: [
          { name: 'Donations', data: [100, 150, 200, 180, 220, 250] },
        ],
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      },
      recentDonations: [
        {
          id: '1',
          donorName: 'John Doe',
          amount: 100,
          organizationName: 'Education Foundation',
          date: '2024-01-15',
          status: 'Completed',
        },
      ],
      topDonors: [
        {
          id: '1',
          name: 'Alice Johnson',
          totalAmount: 1500,
          donationCount: 15,
        },
      ],
      organizations: [
        {
          id: '1',
          name: 'Education Foundation',
          totalDonations: 150,
          totalAmount: 15000,
          isActive: true,
        },
      ],
      ...mockData,
    };

    // Setup GET endpoints
    mockAxios.get.mockImplementation((url) => {
      if (url === '/api/donation-heads') {
        return Promise.resolve({ data: defaultMockData.donationHeads });
      }
      if (url === '/api/admin/dashboard') {
        return Promise.resolve({ data: defaultMockData.dashboardData });
      }
      if (url === '/api/tenant/dashboard') {
        return Promise.resolve({ data: defaultMockData.tenantData });
      }
      if (url.includes('/api/admin/charts') || url.includes('/api/tenant/charts')) {
        return Promise.resolve({ data: defaultMockData.chartData });
      }
      if (url === '/api/admin/recent-donations') {
        return Promise.resolve({ data: defaultMockData.recentDonations });
      }
      if (url === '/api/tenant/top-donors') {
        return Promise.resolve({ data: defaultMockData.topDonors });
      }
      if (url === '/api/admin/organizations' || url === '/api/tenant/donation-heads') {
        return Promise.resolve({ data: defaultMockData.organizations });
      }
      if (url === '/api/tenant/recent-activities') {
        return Promise.resolve({ data: [] });
      }
      return Promise.reject(new Error(`Unmocked endpoint: ${url}`));
    });

    // Setup POST endpoints
    mockAxios.post.mockImplementation((url, data) => {
      if (url === '/api/donation-heads') {
        return Promise.resolve({ 
          data: { 
            id: 'new-id', 
            ...data,
            createdOn: new Date().toISOString(),
            updatedOn: new Date().toISOString(),
          } 
        });
      }
      if (url.includes('/export')) {
        return Promise.resolve({ 
          data: 'mock-export-data',
          headers: { 'content-type': 'application/octet-stream' }
        });
      }
      return Promise.reject(new Error(`Unmocked POST endpoint: ${url}`));
    });

    // Setup PUT endpoints
    mockAxios.put.mockImplementation((url, data) => {
      if (url.includes('/api/donation-heads/')) {
        const id = url.split('/').pop();
        return Promise.resolve({ 
          data: { 
            id,
            ...data,
            updatedOn: new Date().toISOString(),
          } 
        });
      }
      return Promise.reject(new Error(`Unmocked PUT endpoint: ${url}`));
    });

    // Setup DELETE endpoints
    mockAxios.delete.mockImplementation((url) => {
      if (url.includes('/api/donation-heads/')) {
        return Promise.resolve({ data: {} });
      }
      return Promise.reject(new Error(`Unmocked DELETE endpoint: ${url}`));
    });
  };

  const setupFailedApiMocks = (failureType = 'network') => {
    const error = failureType === 'network' 
      ? new Error('Network Error')
      : new Error('Server Error');
    
    mockAxios.get.mockRejectedValue(error);
    mockAxios.post.mockRejectedValue(error);
    mockAxios.put.mockRejectedValue(error);
    mockAxios.delete.mockRejectedValue(error);
  };

  const clearApiMocks = () => {
    mockAxios.get.mockClear();
    mockAxios.post.mockClear();
    mockAxios.put.mockClear();
    mockAxios.delete.mockClear();
  };

  return {
    setupSuccessfulApiMocks,
    setupFailedApiMocks,
    clearApiMocks,
    mockAxios,
  };
};

// Test data generators for integration tests
export const generateIntegrationTestData = {
  donationHead: (overrides = {}) => ({
    id: `test-head-${Date.now()}`,
    name: 'Test Donation Head',
    description: 'Test description for integration testing',
    orgId: 'test-org-1',
    isActive: true,
    createdOn: new Date().toISOString(),
    updatedOn: new Date().toISOString(),
    ...overrides,
  }),

  donationHeadsList: (count = 3) => {
    return Array.from({ length: count }, (_, index) => 
      generateIntegrationTestData.donationHead({
        id: `test-head-${index + 1}`,
        name: `Test Donation Head ${index + 1}`,
        isActive: index % 2 === 0, // Alternate active/inactive
      })
    );
  },

  dashboardStats: (overrides = {}) => ({
    totalDonations: 1000,
    totalAmount: 50000,
    activeOrganizations: 25,
    growthRate: 10.5,
    averageDonation: 50,
    activeDonationHeads: 10,
    ...overrides,
  }),

  chartData: (overrides = {}) => ({
    series: [
      { name: 'Donations', data: [10, 20, 30, 40, 50, 60] },
      { name: 'Amount', data: [500, 1000, 1500, 2000, 2500, 3000] },
    ],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    ...overrides,
  }),

  recentDonation: (overrides = {}) => ({
    id: `donation-${Date.now()}`,
    donorName: 'Test Donor',
    amount: 100,
    organizationName: 'Test Organization',
    date: new Date().toISOString().split('T')[0],
    status: 'Completed',
    ...overrides,
  }),

  topDonor: (overrides = {}) => ({
    id: `donor-${Date.now()}`,
    name: 'Test Donor',
    totalAmount: 1000,
    donationCount: 10,
    ...overrides,
  }),

  organization: (overrides = {}) => ({
    id: `org-${Date.now()}`,
    name: 'Test Organization',
    totalDonations: 100,
    totalAmount: 10000,
    isActive: true,
    ...overrides,
  }),

  activity: (overrides = {}) => ({
    id: `activity-${Date.now()}`,
    type: 'Donation',
    description: 'Test activity description',
    date: new Date().toISOString().split('T')[0],
    user: 'Test User',
    ...overrides,
  }),
};

// Integration test assertion helpers
export const integrationAssertions = {
  expectApiCallsToMatch: (mockAxios, expectedCalls) => {
    expectedCalls.forEach(({ method, url, data }) => {
      if (method === 'GET') {
        expect(mockAxios.get).toHaveBeenCalledWith(url);
      } else if (method === 'POST') {
        expect(mockAxios.post).toHaveBeenCalledWith(url, data);
      } else if (method === 'PUT') {
        expect(mockAxios.put).toHaveBeenCalledWith(url, data);
      } else if (method === 'DELETE') {
        expect(mockAxios.delete).toHaveBeenCalledWith(url);
      }
    });
  },

  expectElementsToBeVisible: (screen, testIds) => {
    testIds.forEach(testId => {
      expect(screen.getByTestId(testId)).toBeInTheDocument();
    });
  },

  expectElementsToHaveText: (screen, expectations) => {
    expectations.forEach(({ testId, text }) => {
      expect(screen.getByTestId(testId)).toHaveTextContent(text);
    });
  },
};

// Export mock contexts for use in integration tests
export { 
  mockIntegrationAuthContext, 
  mockIntegrationRBACContext, 
  mockIntegrationRouter 
};
