/**
 * API Tests for Donation Head Management
 * Tests all CRUD operations and edge cases for donation head endpoints
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Donation Head API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const donationHeadEndpoint = `${baseURL}/donation-heads`;

  // Mock data
  const mockDonationHead = {
    id: '1',
    name: 'Education Fund',
    description: 'Supporting education initiatives',
    orgId: 'org-1',
    isActive: true,
    createdOn: '2024-01-01T00:00:00Z',
    updatedOn: '2024-01-01T00:00:00Z',
  };

  const mockDonationHeadsList = [
    mockDonationHead,
    {
      id: '2',
      name: 'Healthcare Support',
      description: 'Medical assistance program',
      orgId: 'org-1',
      isActive: false,
      createdOn: '2024-01-02T00:00:00Z',
      updatedOn: '2024-01-02T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/donation-heads', () => {
    it('should fetch all donation heads successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockDonationHeadsList,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(donationHeadEndpoint);

      expect(mockedAxios.get).toHaveBeenCalledWith(donationHeadEndpoint);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockDonationHeadsList);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
    });

    it('should handle empty donation heads list', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: [],
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(donationHeadEndpoint);

      expect(response.status).toBe(200);
      expect(response.data).toEqual([]);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(0);
    });

    it('should handle server errors', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 500,
          data: { error: 'Internal Server Error' }
        }
      });

      try {
        await axios.get(donationHeadEndpoint);
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data.error).toBe('Internal Server Error');
      }
    });

    it('should handle network errors', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network Error'));

      try {
        await axios.get(donationHeadEndpoint);
      } catch (error) {
        expect(error.message).toBe('Network Error');
      }
    });

    it('should handle authentication errors', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 401,
          data: { error: 'Unauthorized' }
        }
      });

      try {
        await axios.get(donationHeadEndpoint);
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Unauthorized');
      }
    });
  });

  describe('GET /api/donation-heads/:id', () => {
    it('should fetch a specific donation head successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockDonationHead,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${donationHeadEndpoint}/1`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${donationHeadEndpoint}/1`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockDonationHead);
      expect(response.data.id).toBe('1');
    });

    it('should handle donation head not found', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 404,
          data: { error: 'Donation head not found' }
        }
      });

      try {
        await axios.get(`${donationHeadEndpoint}/999`);
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.error).toBe('Donation head not found');
      }
    });
  });

  describe('POST /api/donation-heads', () => {
    const newDonationHead = {
      name: 'New Education Fund',
      description: 'New education initiative',
      orgId: 'org-1',
      isActive: true,
    };

    it('should create a new donation head successfully', async () => {
      const createdDonationHead = {
        id: '3',
        ...newDonationHead,
        createdOn: new Date().toISOString(),
        updatedOn: new Date().toISOString(),
      };

      mockedAxios.post.mockResolvedValue({ 
        data: createdDonationHead,
        status: 201,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(donationHeadEndpoint, newDonationHead);

      expect(mockedAxios.post).toHaveBeenCalledWith(donationHeadEndpoint, newDonationHead);
      expect(response.status).toBe(201);
      expect(response.data).toEqual(createdDonationHead);
      expect(response.data.id).toBeDefined();
      expect(response.data.name).toBe(newDonationHead.name);
    });

    it('should handle validation errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { 
            error: 'Validation failed',
            details: ['Name is required', 'Organization ID is required']
          }
        }
      });

      try {
        await axios.post(donationHeadEndpoint, { name: '' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Validation failed');
        expect(Array.isArray(error.response.data.details)).toBe(true);
      }
    });

    it('should handle duplicate name errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: { error: 'Donation head with this name already exists' }
        }
      });

      try {
        await axios.post(donationHeadEndpoint, newDonationHead);
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.error).toBe('Donation head with this name already exists');
      }
    });
  });

  describe('PUT /api/donation-heads/:id', () => {
    const updatedData = {
      name: 'Updated Education Fund',
      description: 'Updated description',
      isActive: false,
    };

    it('should update a donation head successfully', async () => {
      const updatedDonationHead = {
        ...mockDonationHead,
        ...updatedData,
        updatedOn: new Date().toISOString(),
      };

      mockedAxios.put.mockResolvedValue({ 
        data: updatedDonationHead,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.put(`${donationHeadEndpoint}/1`, updatedData);

      expect(mockedAxios.put).toHaveBeenCalledWith(`${donationHeadEndpoint}/1`, updatedData);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(updatedDonationHead);
      expect(response.data.name).toBe(updatedData.name);
    });

    it('should handle update validation errors', async () => {
      mockedAxios.put.mockRejectedValue({
        response: {
          status: 400,
          data: { 
            error: 'Validation failed',
            details: ['Name cannot be empty']
          }
        }
      });

      try {
        await axios.put(`${donationHeadEndpoint}/1`, { name: '' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Validation failed');
      }
    });

    it('should handle update not found errors', async () => {
      mockedAxios.put.mockRejectedValue({
        response: {
          status: 404,
          data: { error: 'Donation head not found' }
        }
      });

      try {
        await axios.put(`${donationHeadEndpoint}/999`, updatedData);
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.error).toBe('Donation head not found');
      }
    });
  });

  describe('DELETE /api/donation-heads/:id', () => {
    it('should delete a donation head successfully', async () => {
      mockedAxios.delete.mockResolvedValue({ 
        data: { message: 'Donation head deleted successfully' },
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.delete(`${donationHeadEndpoint}/1`);

      expect(mockedAxios.delete).toHaveBeenCalledWith(`${donationHeadEndpoint}/1`);
      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Donation head deleted successfully');
    });

    it('should handle delete not found errors', async () => {
      mockedAxios.delete.mockRejectedValue({
        response: {
          status: 404,
          data: { error: 'Donation head not found' }
        }
      });

      try {
        await axios.delete(`${donationHeadEndpoint}/999`);
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.error).toBe('Donation head not found');
      }
    });

    it('should handle delete constraint errors', async () => {
      mockedAxios.delete.mockRejectedValue({
        response: {
          status: 409,
          data: { error: 'Cannot delete donation head with existing donations' }
        }
      });

      try {
        await axios.delete(`${donationHeadEndpoint}/1`);
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.error).toBe('Cannot delete donation head with existing donations');
      }
    });
  });

  describe('Query Parameters and Filtering', () => {
    it('should handle search query parameters', async () => {
      const filteredResults = [mockDonationHead];
      mockedAxios.get.mockResolvedValue({ 
        data: filteredResults,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${donationHeadEndpoint}?search=Education`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${donationHeadEndpoint}?search=Education`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(filteredResults);
    });

    it('should handle status filtering', async () => {
      const activeHeads = [mockDonationHead];
      mockedAxios.get.mockResolvedValue({ 
        data: activeHeads,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${donationHeadEndpoint}?isActive=true`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${donationHeadEndpoint}?isActive=true`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(activeHeads);
    });

    it('should handle pagination parameters', async () => {
      const paginatedResults = {
        data: [mockDonationHead],
        total: 10,
        page: 1,
        limit: 5,
        totalPages: 2
      };

      mockedAxios.get.mockResolvedValue({ 
        data: paginatedResults,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${donationHeadEndpoint}?page=1&limit=5`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${donationHeadEndpoint}?page=1&limit=5`);
      expect(response.status).toBe(200);
      expect(response.data.data).toEqual([mockDonationHead]);
      expect(response.data.total).toBe(10);
      expect(response.data.page).toBe(1);
    });
  });

  describe('Request Headers and Authentication', () => {
    it('should include authorization headers', async () => {
      const token = 'Bearer mock-jwt-token';
      mockedAxios.get.mockResolvedValue({ 
        data: mockDonationHeadsList,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      await axios.get(donationHeadEndpoint, {
        headers: { Authorization: token }
      });

      expect(mockedAxios.get).toHaveBeenCalledWith(donationHeadEndpoint, {
        headers: { Authorization: token }
      });
    });

    it('should handle content-type headers for POST requests', async () => {
      const newDonationHead = {
        name: 'Test Fund',
        description: 'Test description',
        orgId: 'org-1',
        isActive: true,
      };

      mockedAxios.post.mockResolvedValue({ 
        data: { id: '3', ...newDonationHead },
        status: 201,
        headers: { 'content-type': 'application/json' }
      });

      await axios.post(donationHeadEndpoint, newDonationHead, {
        headers: { 'Content-Type': 'application/json' }
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(donationHeadEndpoint, newDonationHead, {
        headers: { 'Content-Type': 'application/json' }
      });
    });
  });
});
