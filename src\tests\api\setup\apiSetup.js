// API test setup file
import 'whatwg-fetch';

// Mock console methods to reduce noise in API tests
const originalError = console.error;
const originalWarn = console.warn;
const originalLog = console.log;

beforeAll(() => {
  console.error = (...args) => {
    // Suppress specific warnings/errors that are expected in API tests
    const message = args[0];
    if (
      typeof message === 'string' &&
      (
        message.includes('Warning: ReactDOM.render is deprecated') ||
        message.includes('Network Error') ||
        message.includes('Request failed')
      )
    ) {
      return;
    }
    originalError.apply(console, args);
  };

  console.warn = (...args) => {
    const message = args[0];
    if (
      typeof message === 'string' &&
      (
        message.includes('deprecated') ||
        message.includes('Warning:')
      )
    ) {
      return;
    }
    originalWarn.apply(console, args);
  };

  // Optionally suppress console.log in API tests
  if (process.env.SUPPRESS_API_LOGS === 'true') {
    console.log = () => {};
  }
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
  console.log = originalLog;
});

// Mock environment variables for API tests
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';
process.env.API_SECRET_KEY = 'test-secret-key';
process.env.JWT_SECRET = 'test-jwt-secret';

// Mock fetch for API tests if needed
global.fetch = require('jest-fetch-mock');

// Mock localStorage for API tests
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage for API tests
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Global test utilities for API tests
global.apiTestUtils = {
  createMockRequest: (method = 'GET', url = '/', data = null, headers = {}) => ({
    method,
    url,
    data,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  }),
  
  createMockResponse: (data = {}, status = 200, headers = {}) => ({
    data,
    status,
    headers: {
      'content-type': 'application/json',
      ...headers,
    },
  }),
  
  createMockError: (status = 500, message = 'Internal Server Error', details = null) => ({
    response: {
      status,
      data: {
        error: message,
        ...(details && { details }),
      },
    },
  }),
  
  createAuthHeaders: (token = 'mock-jwt-token') => ({
    Authorization: `Bearer ${token}`,
  }),
  
  createPaginationParams: (page = 1, limit = 10) => ({
    page,
    limit,
  }),
  
  createDateRangeParams: (startDate = '2024-01-01', endDate = '2024-12-31') => ({
    startDate,
    endDate,
  }),
  
  waitForApiCall: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
};

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
  
  // Reset fetch mock if used
  if (global.fetch && global.fetch.resetMocks) {
    global.fetch.resetMocks();
  }
  
  // Clean up any global state
  delete global.mockApiData;
  delete global.testApiState;
});
