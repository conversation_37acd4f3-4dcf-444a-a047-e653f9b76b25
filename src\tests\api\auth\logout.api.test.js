/**
 * API Tests for Logout Authentication
 * Tests all logout-related API endpoints and session management
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Logout API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const logoutEndpoint = `${baseURL}/auth/logout`;

  // Mock data
  const mockAuthHeaders = {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    'Content-Type': 'application/json',
  };

  const mockLogoutRequest = {
    refreshToken: 'refresh_token_here',
    logoutFromAllDevices: false,
  };

  const mockLogoutResponse = {
    success: true,
    message: 'Logout successful',
    data: {
      loggedOutAt: '2024-01-15T10:30:00Z',
      sessionId: 'session-123',
      devicesLoggedOut: 1,
    },
  };

  const mockLogoutAllDevicesResponse = {
    success: true,
    message: 'Logged out from all devices successfully',
    data: {
      loggedOutAt: '2024-01-15T10:30:00Z',
      devicesLoggedOut: 3,
      sessionIds: ['session-123', 'session-456', 'session-789'],
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully from current device', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockLogoutResponse,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(logoutEndpoint, mockLogoutRequest, {
        headers: mockAuthHeaders,
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        logoutEndpoint, 
        mockLogoutRequest,
        { headers: mockAuthHeaders }
      );
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockLogoutResponse);
      expect(response.data.success).toBe(true);
      expect(response.data.data.devicesLoggedOut).toBe(1);
    });

    it('should logout from all devices', async () => {
      const logoutAllRequest = {
        ...mockLogoutRequest,
        logoutFromAllDevices: true,
      };

      mockedAxios.post.mockResolvedValue({ 
        data: mockLogoutAllDevicesResponse,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(logoutEndpoint, logoutAllRequest, {
        headers: mockAuthHeaders,
      });

      expect(mockedAxios.post).toHaveBeenCalledWith(
        logoutEndpoint, 
        logoutAllRequest,
        { headers: mockAuthHeaders }
      );
      expect(response.status).toBe(200);
      expect(response.data.data.devicesLoggedOut).toBe(3);
      expect(response.data.data.sessionIds).toHaveLength(3);
    });

    it('should handle unauthorized logout attempt', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Invalid or expired token',
            code: 'UNAUTHORIZED',
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest, {
          headers: {
            'Authorization': 'Bearer invalid_token',
            'Content-Type': 'application/json',
          },
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.success).toBe(false);
        expect(error.response.data.code).toBe('UNAUTHORIZED');
      }
    });

    it('should handle missing authorization header', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 401,
          data: {
            success: false,
            message: 'Authorization header is required',
            code: 'MISSING_AUTH_HEADER',
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest);
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.code).toBe('MISSING_AUTH_HEADER');
      }
    });

    it('should handle invalid refresh token', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid refresh token',
            code: 'INVALID_REFRESH_TOKEN',
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, {
          ...mockLogoutRequest,
          refreshToken: 'invalid_refresh_token',
        }, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('INVALID_REFRESH_TOKEN');
      }
    });

    it('should handle already logged out session', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: {
            success: false,
            message: 'Session already terminated',
            code: 'SESSION_ALREADY_TERMINATED',
            data: {
              terminatedAt: '2024-01-15T09:00:00Z',
            },
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.code).toBe('SESSION_ALREADY_TERMINATED');
        expect(error.response.data.data.terminatedAt).toBeDefined();
      }
    });

    it('should handle server errors during logout', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error during logout',
            code: 'LOGOUT_ERROR',
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data.code).toBe('LOGOUT_ERROR');
      }
    });

    it('should handle network errors', async () => {
      mockedAxios.post.mockRejectedValue(new Error('Network Error'));

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.message).toBe('Network Error');
      }
    });
  });

  describe('Session Management', () => {
    it('should invalidate session tokens', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            tokensInvalidated: {
              accessToken: true,
              refreshToken: true,
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, mockLogoutRequest, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.tokensInvalidated.accessToken).toBe(true);
      expect(response.data.data.tokensInvalidated.refreshToken).toBe(true);
    });

    it('should handle partial logout failure', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          success: true,
          message: 'Logout completed with warnings',
          data: {
            loggedOutAt: '2024-01-15T10:30:00Z',
            devicesLoggedOut: 2,
            devicesFailedToLogout: 1,
            warnings: ['Failed to invalidate session on device: Mobile App'],
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, {
        ...mockLogoutRequest,
        logoutFromAllDevices: true,
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.devicesLoggedOut).toBe(2);
      expect(response.data.data.devicesFailedToLogout).toBe(1);
      expect(response.data.data.warnings).toHaveLength(1);
    });

    it('should track logout analytics', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            analytics: {
              sessionDuration: 3600, // 1 hour in seconds
              actionsPerformed: 25,
              lastActivity: '2024-01-15T10:25:00Z',
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, mockLogoutRequest, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.analytics.sessionDuration).toBe(3600);
      expect(response.data.data.analytics.actionsPerformed).toBe(25);
    });
  });

  describe('Security Features', () => {
    it('should handle forced logout by admin', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          success: true,
          message: 'User logged out by administrator',
          data: {
            loggedOutAt: '2024-01-15T10:30:00Z',
            reason: 'Administrative action',
            forcedBy: '<EMAIL>',
            devicesLoggedOut: 3,
          },
        },
        status: 200,
      });

      const response = await axios.post(`${baseURL}/admin/force-logout`, {
        userId: 'user-123',
        reason: 'Security violation',
      }, {
        headers: {
          'Authorization': 'Bearer admin_token',
          'Content-Type': 'application/json',
        },
      });

      expect(response.data.data.reason).toBe('Administrative action');
      expect(response.data.data.forcedBy).toBe('<EMAIL>');
    });

    it('should handle suspicious activity logout', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          success: true,
          message: 'Logged out due to suspicious activity',
          data: {
            loggedOutAt: '2024-01-15T10:30:00Z',
            reason: 'Suspicious activity detected',
            securityAlert: true,
            recommendedActions: [
              'Change password',
              'Review recent activity',
              'Enable two-factor authentication',
            ],
          },
        },
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/security-logout`, {
        reason: 'Unusual login location',
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.securityAlert).toBe(true);
      expect(response.data.data.recommendedActions).toHaveLength(3);
    });

    it('should handle logout with audit trail', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            auditTrail: {
              userId: 'user-123',
              ipAddress: '***********',
              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
              logoutMethod: 'manual',
              timestamp: '2024-01-15T10:30:00Z',
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, mockLogoutRequest, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.auditTrail.userId).toBe('user-123');
      expect(response.data.data.auditTrail.logoutMethod).toBe('manual');
    });
  });

  describe('Different Logout Scenarios', () => {
    it('should handle logout from mobile app', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            deviceInfo: {
              type: 'mobile',
              platform: 'iOS',
              appVersion: '1.2.3',
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, {
        ...mockLogoutRequest,
        deviceType: 'mobile',
        platform: 'iOS',
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.deviceInfo.type).toBe('mobile');
      expect(response.data.data.deviceInfo.platform).toBe('iOS');
    });

    it('should handle logout from web browser', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            deviceInfo: {
              type: 'web',
              browser: 'Chrome',
              browserVersion: '120.0.0.0',
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, {
        ...mockLogoutRequest,
        deviceType: 'web',
        browser: 'Chrome',
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.deviceInfo.type).toBe('web');
      expect(response.data.data.deviceInfo.browser).toBe('Chrome');
    });

    it('should handle automatic logout due to inactivity', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          success: true,
          message: 'Logged out due to inactivity',
          data: {
            loggedOutAt: '2024-01-15T10:30:00Z',
            reason: 'Session timeout',
            inactivityDuration: 1800, // 30 minutes
            lastActivity: '2024-01-15T10:00:00Z',
          },
        },
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/timeout-logout`, {
        sessionId: 'session-123',
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.reason).toBe('Session timeout');
      expect(response.data.data.inactivityDuration).toBe(1800);
    });

    it('should handle logout with data cleanup', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockLogoutResponse,
          data: {
            ...mockLogoutResponse.data,
            dataCleanup: {
              tempFilesDeleted: 5,
              cacheCleared: true,
              sessionDataRemoved: true,
            },
          },
        },
        status: 200,
      });

      const response = await axios.post(logoutEndpoint, {
        ...mockLogoutRequest,
        cleanupData: true,
      }, {
        headers: mockAuthHeaders,
      });

      expect(response.data.data.dataCleanup.tempFilesDeleted).toBe(5);
      expect(response.data.data.dataCleanup.cacheCleared).toBe(true);
    });
  });

  describe('Rate Limiting and Security', () => {
    it('should handle logout rate limiting', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 429,
          data: {
            success: false,
            message: 'Too many logout requests',
            code: 'RATE_LIMITED',
            data: {
              retryAfter: 60,
              maxAttempts: 10,
            },
          },
          headers: {
            'retry-after': '60',
          },
        }
      });

      try {
        await axios.post(logoutEndpoint, mockLogoutRequest, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data.code).toBe('RATE_LIMITED');
        expect(error.response.headers['retry-after']).toBe('60');
      }
    });

    it('should include security headers in logout response', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockLogoutResponse,
        status: 200,
        headers: { 
          'content-type': 'application/json',
          'x-frame-options': 'DENY',
          'x-content-type-options': 'nosniff',
          'cache-control': 'no-store',
        }
      });

      const response = await axios.post(logoutEndpoint, mockLogoutRequest, {
        headers: mockAuthHeaders,
      });

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['cache-control']).toBe('no-store');
    });
  });

  describe('Logout Validation', () => {
    it('should validate logout request format', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid logout request format',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'refreshToken', message: 'Refresh token is required' },
            ],
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, {
          logoutFromAllDevices: false,
          // Missing refreshToken
        }, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
      }
    });

    it('should validate boolean parameters', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid parameter type',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'logoutFromAllDevices', message: 'Must be a boolean value' },
            ],
          }
        }
      });

      try {
        await axios.post(logoutEndpoint, {
          ...mockLogoutRequest,
          logoutFromAllDevices: 'invalid',
        }, {
          headers: mockAuthHeaders,
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
      }
    });
  });
});
