/**
 * API Tests for Donation Admin Dashboard
 * Tests all admin-specific endpoints including dashboard data, charts, and exports
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Donation Admin API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const adminEndpoint = `${baseURL}/admin`;

  // Mock data
  const mockDashboardData = {
    totalDonations: 1500,
    totalAmount: 75000,
    activeOrganizations: 30,
    growthRate: 12.5,
    averageDonation: 50,
    topDonationHead: 'Education Fund',
    recentDonationsCount: 25,
    monthlyGrowth: 8.3,
  };

  const mockChartData = {
    series: [
      { name: 'Donations', data: [100, 150, 200, 180, 220, 250] },
      { name: 'Amount', data: [5000, 7500, 10000, 9000, 11000, 12500] },
    ],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    colors: ['#1976d2', '#dc004e'],
  };

  const mockRecentDonations = [
    {
      id: '1',
      donorName: '<PERSON>',
      amount: 100,
      organizationName: 'Education Foundation',
      date: '2024-01-15',
      status: 'Completed',
      receiptNumber: 'RCP-001',
    },
    {
      id: '2',
      donorName: 'Jane Smith',
      amount: 250,
      organizationName: 'Healthcare Support',
      date: '2024-01-14',
      status: 'Pending',
      receiptNumber: 'RCP-002',
    },
  ];

  const mockOrganizations = [
    {
      id: '1',
      name: 'Education Foundation',
      totalDonations: 150,
      totalAmount: 15000,
      isActive: true,
      lastDonation: '2024-01-15',
    },
    {
      id: '2',
      name: 'Healthcare Support',
      totalDonations: 200,
      totalAmount: 25000,
      isActive: true,
      lastDonation: '2024-01-14',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/admin/dashboard', () => {
    it('should fetch dashboard statistics successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockDashboardData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/dashboard`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/dashboard`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockDashboardData);
      expect(typeof response.data.totalDonations).toBe('number');
      expect(typeof response.data.totalAmount).toBe('number');
      expect(typeof response.data.growthRate).toBe('number');
    });

    it('should handle dashboard data with date range filters', async () => {
      const filteredData = { ...mockDashboardData, totalDonations: 500 };
      mockedAxios.get.mockResolvedValue({ 
        data: filteredData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/dashboard?startDate=2024-01-01&endDate=2024-01-31`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/dashboard?startDate=2024-01-01&endDate=2024-01-31`);
      expect(response.status).toBe(200);
      expect(response.data.totalDonations).toBe(500);
    });

    it('should handle unauthorized access', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 403,
          data: { error: 'Insufficient permissions for admin dashboard' }
        }
      });

      try {
        await axios.get(`${adminEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.error).toBe('Insufficient permissions for admin dashboard');
      }
    });
  });

  describe('GET /api/admin/charts', () => {
    it('should fetch chart data successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockChartData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/charts`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/charts`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockChartData);
      expect(Array.isArray(response.data.series)).toBe(true);
      expect(Array.isArray(response.data.categories)).toBe(true);
    });

    it('should handle chart data with period parameter', async () => {
      const monthlyData = { ...mockChartData, period: 'monthly' };
      mockedAxios.get.mockResolvedValue({ 
        data: monthlyData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/charts?period=monthly`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/charts?period=monthly`);
      expect(response.status).toBe(200);
      expect(response.data.period).toBe('monthly');
    });

    it('should handle chart type parameter', async () => {
      const barChartData = { ...mockChartData, type: 'bar' };
      mockedAxios.get.mockResolvedValue({ 
        data: barChartData,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/charts?type=bar`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/charts?type=bar`);
      expect(response.status).toBe(200);
      expect(response.data.type).toBe('bar');
    });
  });

  describe('GET /api/admin/recent-donations', () => {
    it('should fetch recent donations successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockRecentDonations,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/recent-donations`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/recent-donations`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockRecentDonations);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
    });

    it('should handle recent donations with limit parameter', async () => {
      const limitedDonations = [mockRecentDonations[0]];
      mockedAxios.get.mockResolvedValue({ 
        data: limitedDonations,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/recent-donations?limit=1`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/recent-donations?limit=1`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(1);
    });

    it('should handle recent donations with status filter', async () => {
      const completedDonations = mockRecentDonations.filter(d => d.status === 'Completed');
      mockedAxios.get.mockResolvedValue({ 
        data: completedDonations,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/recent-donations?status=Completed`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/recent-donations?status=Completed`);
      expect(response.status).toBe(200);
      expect(response.data.every(d => d.status === 'Completed')).toBe(true);
    });
  });

  describe('GET /api/admin/organizations', () => {
    it('should fetch organizations successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockOrganizations,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/organizations`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/organizations`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockOrganizations);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data).toHaveLength(2);
    });

    it('should handle organizations with active filter', async () => {
      const activeOrgs = mockOrganizations.filter(org => org.isActive);
      mockedAxios.get.mockResolvedValue({ 
        data: activeOrgs,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/organizations?isActive=true`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/organizations?isActive=true`);
      expect(response.status).toBe(200);
      expect(response.data.every(org => org.isActive)).toBe(true);
    });

    it('should handle organizations sorting', async () => {
      const sortedOrgs = [...mockOrganizations].sort((a, b) => b.totalAmount - a.totalAmount);
      mockedAxios.get.mockResolvedValue({ 
        data: sortedOrgs,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/organizations?sortBy=totalAmount&order=desc`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/organizations?sortBy=totalAmount&order=desc`);
      expect(response.status).toBe(200);
      expect(response.data[0].totalAmount).toBeGreaterThanOrEqual(response.data[1].totalAmount);
    });
  });

  describe('POST /api/admin/export', () => {
    it('should export data as CSV successfully', async () => {
      const csvData = 'Name,Amount,Date\nJohn Doe,100,2024-01-15\nJane Smith,250,2024-01-14';
      mockedAxios.post.mockResolvedValue({ 
        data: csvData,
        status: 200,
        headers: { 
          'content-type': 'text/csv',
          'content-disposition': 'attachment; filename="admin-report.csv"'
        }
      });

      const exportRequest = {
        format: 'csv',
        period: 'monthly',
        includeFields: ['name', 'amount', 'date']
      };

      const response = await axios.post(`${adminEndpoint}/export`, exportRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${adminEndpoint}/export`, exportRequest);
      expect(response.status).toBe(200);
      expect(response.data).toBe(csvData);
      expect(response.headers['content-type']).toBe('text/csv');
    });

    it('should export data as PDF successfully', async () => {
      const pdfData = 'mock-pdf-binary-data';
      mockedAxios.post.mockResolvedValue({ 
        data: pdfData,
        status: 200,
        headers: { 
          'content-type': 'application/pdf',
          'content-disposition': 'attachment; filename="admin-report.pdf"'
        }
      });

      const exportRequest = {
        format: 'pdf',
        period: 'yearly',
        includeCharts: true
      };

      const response = await axios.post(`${adminEndpoint}/export`, exportRequest);

      expect(mockedAxios.post).toHaveBeenCalledWith(`${adminEndpoint}/export`, exportRequest);
      expect(response.status).toBe(200);
      expect(response.data).toBe(pdfData);
      expect(response.headers['content-type']).toBe('application/pdf');
    });

    it('should handle export validation errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { 
            error: 'Invalid export parameters',
            details: ['Format must be csv or pdf', 'Period is required']
          }
        }
      });

      try {
        await axios.post(`${adminEndpoint}/export`, { format: 'invalid' });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Invalid export parameters');
        expect(Array.isArray(error.response.data.details)).toBe(true);
      }
    });

    it('should handle export processing errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: { error: 'Failed to generate export file' }
        }
      });

      try {
        await axios.post(`${adminEndpoint}/export`, { format: 'csv', period: 'monthly' });
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data.error).toBe('Failed to generate export file');
      }
    });
  });

  describe('GET /api/admin/users', () => {
    const mockUsers = [
      {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        lastLogin: '2024-01-15T10:00:00Z',
      },
      {
        id: '2',
        name: 'Regular User',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        lastLogin: '2024-01-14T15:30:00Z',
      },
    ];

    it('should fetch users successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockUsers,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/users`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/users`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockUsers);
      expect(Array.isArray(response.data)).toBe(true);
    });

    it('should handle users with role filter', async () => {
      const adminUsers = mockUsers.filter(user => user.role === 'admin');
      mockedAxios.get.mockResolvedValue({ 
        data: adminUsers,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/users?role=admin`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/users?role=admin`);
      expect(response.status).toBe(200);
      expect(response.data.every(user => user.role === 'admin')).toBe(true);
    });
  });

  describe('GET /api/admin/audit-logs', () => {
    const mockAuditLogs = [
      {
        id: '1',
        action: 'CREATE_DONATION_HEAD',
        userId: 'user-1',
        userName: 'Admin User',
        timestamp: '2024-01-15T10:00:00Z',
        details: { donationHeadId: 'head-1', name: 'Education Fund' },
      },
      {
        id: '2',
        action: 'UPDATE_ORGANIZATION',
        userId: 'user-2',
        userName: 'Manager User',
        timestamp: '2024-01-14T15:30:00Z',
        details: { organizationId: 'org-1', changes: ['name', 'status'] },
      },
    ];

    it('should fetch audit logs successfully', async () => {
      mockedAxios.get.mockResolvedValue({ 
        data: mockAuditLogs,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/audit-logs`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/audit-logs`);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockAuditLogs);
      expect(Array.isArray(response.data)).toBe(true);
    });

    it('should handle audit logs with date range filter', async () => {
      const filteredLogs = [mockAuditLogs[0]];
      mockedAxios.get.mockResolvedValue({ 
        data: filteredLogs,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/audit-logs?startDate=2024-01-15&endDate=2024-01-15`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/audit-logs?startDate=2024-01-15&endDate=2024-01-15`);
      expect(response.status).toBe(200);
      expect(response.data).toHaveLength(1);
    });

    it('should handle audit logs with action filter', async () => {
      const createActions = mockAuditLogs.filter(log => log.action.includes('CREATE'));
      mockedAxios.get.mockResolvedValue({ 
        data: createActions,
        status: 200,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.get(`${adminEndpoint}/audit-logs?action=CREATE`);

      expect(mockedAxios.get).toHaveBeenCalledWith(`${adminEndpoint}/audit-logs?action=CREATE`);
      expect(response.status).toBe(200);
      expect(response.data.every(log => log.action.includes('CREATE'))).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle rate limiting', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 429,
          data: { error: 'Too many requests' },
          headers: { 'retry-after': '60' }
        }
      });

      try {
        await axios.get(`${adminEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data.error).toBe('Too many requests');
        expect(error.response.headers['retry-after']).toBe('60');
      }
    });

    it('should handle server maintenance mode', async () => {
      mockedAxios.get.mockRejectedValue({
        response: {
          status: 503,
          data: { error: 'Service temporarily unavailable' }
        }
      });

      try {
        await axios.get(`${adminEndpoint}/dashboard`);
      } catch (error) {
        expect(error.response.status).toBe(503);
        expect(error.response.data.error).toBe('Service temporarily unavailable');
      }
    });

    it('should handle malformed request data', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 422,
          data: { error: 'Unprocessable entity', details: 'Invalid JSON format' }
        }
      });

      try {
        await axios.post(`${adminEndpoint}/export`, 'invalid-json');
      } catch (error) {
        expect(error.response.status).toBe(422);
        expect(error.response.data.error).toBe('Unprocessable entity');
      }
    });
  });
});
