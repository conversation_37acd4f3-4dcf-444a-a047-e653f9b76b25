<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754127056962" clover="3.2.0">
  <project timestamp="1754127056962" name="All files">
    <metrics statements="26" coveredstatements="19" conditionals="6" coveredconditionals="3" methods="22" coveredmethods="5" elements="54" coveredelements="27" complexity="0" loc="26" ncloc="26" packages="1" files="1" classes="1"/>
    <file name="donationTestUtils.js" path="C:\Users\<USER>\Downloads\Donation-Receipt-Frontend-Application\src\tests\utils\donationTestUtils.js">
      <metrics statements="26" coveredstatements="19" conditionals="6" coveredconditionals="3" methods="22" coveredmethods="5"/>
      <line num="1" count="9" type="stmt"/>
      <line num="2" count="9" type="stmt"/>
      <line num="3" count="9" type="stmt"/>
      <line num="4" count="9" type="stmt"/>
      <line num="7" count="9" type="stmt"/>
      <line num="20" count="9" type="stmt"/>
      <line num="35" count="9" type="stmt"/>
      <line num="36" count="0" type="stmt"/>
      <line num="37" count="0" type="stmt"/>
      <line num="38" count="0" type="stmt"/>
      <line num="39" count="0" type="stmt"/>
      <line num="40" count="0" type="stmt"/>
      <line num="45" count="9" type="stmt"/>
      <line num="61" count="9" type="cond" truecount="2" falsecount="0"/>
      <line num="71" count="121" type="cond" truecount="1" falsecount="0"/>
      <line num="72" count="121" type="stmt"/>
      <line num="83" count="121" type="stmt"/>
      <line num="90" count="9" type="stmt"/>
      <line num="91" count="0" type="stmt"/>
      <line num="95" count="27" type="cond" truecount="0" falsecount="1"/>
      <line num="106" count="27" type="cond" truecount="0" falsecount="1"/>
      <line num="113" count="9" type="cond" truecount="0" falsecount="1"/>
      <line num="122" count="9" type="stmt"/>
      <line num="128" count="9" type="stmt"/>
      <line num="135" count="9" type="stmt"/>
      <line num="142" count="0" type="stmt"/>
    </file>
  </project>
</coverage>
