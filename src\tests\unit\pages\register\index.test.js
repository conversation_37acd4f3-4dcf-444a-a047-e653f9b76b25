/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import { AuthContext } from '../../../../context/AuthContext';
import RegisterPage from '../../../../pages/register/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock dependencies
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('src/@core/components/custom-components/FetchIpAddress', () => ({
  fetchIpAddress: jest.fn(() => Promise.resolve('***********')),
}), { virtual: true });

jest.mock('src/@core/components/spinner', () => {
  return function FallbackSpinner() {
    return <div data-testid="fallback-spinner">Loading...</div>;
  };
}, { virtual: true });

jest.mock('axios', () => ({
  post: jest.fn(),
  get: jest.fn(),
}));

describe('RegisterPage Unit Tests', () => {
  const mockPush = jest.fn();
  const mockHandleRegister = jest.fn();
  const user = userEvent.setup();

  const mockAuthContext = {
    handleRegister: mockHandleRegister,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue({
      push: mockPush,
      query: { role: 'donor' },
    });
  });

  const renderRegisterPage = (authContextValue = mockAuthContext) => {
    return renderWithProviders(
      <AuthContext.Provider value={authContextValue}>
        <RegisterPage />
      </AuthContext.Provider>
    );
  };

  describe('Component Rendering', () => {
    it('should render registration form elements', () => {
      renderRegisterPage();

      expect(screen.getByText('Register')).toBeInTheDocument();
      expect(screen.getByText('Adventure starts here 🚀')).toBeInTheDocument();
      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    });

    it('should render role-specific fields for donor', () => {
      useRouter.mockReturnValue({
        push: mockPush,
        query: { role: 'donor' },
      });

      renderRegisterPage();

      expect(screen.getByText('Donor Registration')).toBeInTheDocument();
    });

    it('should render role-specific fields for NGO', () => {
      useRouter.mockReturnValue({
        push: mockPush,
        query: { role: 'ngo' },
      });

      renderRegisterPage();

      expect(screen.getByText('NGO Registration')).toBeInTheDocument();
      expect(screen.getByLabelText(/organization name/i)).toBeInTheDocument();
    });

    it('should render terms and conditions checkbox', () => {
      renderRegisterPage();

      const termsCheckbox = screen.getByRole('checkbox');
      expect(termsCheckbox).toBeInTheDocument();
      expect(screen.getByText(/I agree to/)).toBeInTheDocument();
      expect(screen.getByText('privacy policy & terms')).toBeInTheDocument();
    });

    it('should render submit button', () => {
      renderRegisterPage();

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      expect(submitButton).toBeInTheDocument();
    });

    it('should render login link', () => {
      renderRegisterPage();

      expect(screen.getByText('Already have an account?')).toBeInTheDocument();
      expect(screen.getByText('Sign in instead')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show required error for empty first name', async () => {
      renderRegisterPage();

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('First name is required')).toBeInTheDocument();
      });
    });

    it('should show required error for empty last name', async () => {
      renderRegisterPage();

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Last name is required')).toBeInTheDocument();
      });
    });

    it('should show email validation error for invalid email', async () => {
      renderRegisterPage();

      const emailInput = screen.getByLabelText(/email/i);
      await user.type(emailInput, 'invalid-email');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Enter a valid email address')).toBeInTheDocument();
      });
    });

    it('should show mobile number validation error', async () => {
      renderRegisterPage();

      const mobileInput = screen.getByLabelText(/mobile number/i);
      await user.type(mobileInput, '123');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Mobile number must be 10 digits')).toBeInTheDocument();
      });
    });

    it('should show password validation error for weak password', async () => {
      renderRegisterPage();

      const passwordInput = screen.getByLabelText(/password/i);
      await user.type(passwordInput, '123');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 8 characters')).toBeInTheDocument();
      });
    });

    it('should show terms acceptance error', async () => {
      renderRegisterPage();

      const firstNameInput = screen.getByLabelText(/first name/i);
      await user.type(firstNameInput, 'John');

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('You must accept the terms and conditions')).toBeInTheDocument();
      });
    });

    it('should accept valid form data', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      await waitFor(() => {
        expect(screen.queryByText('First name is required')).not.toBeInTheDocument();
        expect(screen.queryByText('Enter a valid email address')).not.toBeInTheDocument();
      });
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle password visibility', async () => {
      renderRegisterPage();

      const passwordInput = screen.getByLabelText(/password/i);
      const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button

      // Initially password should be hidden
      expect(passwordInput).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');

      // Click to hide password again
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  describe('OTP Functionality', () => {
    it('should open OTP dialog for email verification', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
        expect(screen.getByText(/OTP has been sent to/)).toBeInTheDocument();
      });
    });

    it('should open OTP dialog for mobile verification', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      
      const verifyMobileButton = screen.getByRole('button', { name: /verify mobile/i });
      await user.click(verifyMobileButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
        expect(screen.getByText(/OTP has been sent to/)).toBeInTheDocument();
      });
    });

    it('should handle OTP input', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByLabelText('OTP')).toBeInTheDocument();
      });

      const otpInput = screen.getByLabelText('OTP');
      await user.type(otpInput, '123456');

      expect(otpInput).toHaveValue('123456');
    });

    it('should validate OTP', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByLabelText('OTP')).toBeInTheDocument();
      });

      const otpInput = screen.getByLabelText('OTP');
      await user.type(otpInput, '123456');

      const validateButton = screen.getByRole('button', { name: /validate/i });
      await user.click(validateButton);

      // Should call validation logic
      expect(validateButton).toBeInTheDocument();
    });

    it('should close OTP dialog', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        expect(screen.getByText('Verify OTP')).toBeInTheDocument();
      });

      const closeButton = screen.getByRole('button', { name: '' }); // Close icon
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('Verify OTP')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should submit form with valid data', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback, successCallback) => {
        successCallback({ message: 'Registration successful' });
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockHandleRegister).toHaveBeenCalledWith(
          expect.objectContaining({
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            mobileNumber: '9876543210',
            password: 'password123',
            role: 'donor',
          }),
          expect.any(Function),
          expect.any(Function)
        );
      });
    });

    it('should handle registration success', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback, successCallback) => {
        successCallback({ message: 'Registration successful' });
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Registration successful')).toBeInTheDocument();
      });
    });

    it('should handle registration failure', async () => {
      mockHandleRegister.mockImplementation((data, errorCallback) => {
        errorCallback('Email already exists');
      });

      renderRegisterPage();

      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email already exists')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('should navigate to login page', async () => {
      renderRegisterPage();

      const loginLink = screen.getByText('Sign in instead');
      await user.click(loginLink);

      expect(mockPush).toHaveBeenCalledWith('/login');
    });
  });

  describe('Role-specific Functionality', () => {
    it('should handle NGO-specific fields', () => {
      useRouter.mockReturnValue({
        push: mockPush,
        query: { role: 'ngo' },
      });

      renderRegisterPage();

      expect(screen.getByLabelText(/organization name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/organization type/i)).toBeInTheDocument();
    });

    it('should validate NGO-specific fields', async () => {
      useRouter.mockReturnValue({
        push: mockPush,
        query: { role: 'ngo' },
      });

      renderRegisterPage();

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Organization name is required')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockHandleRegister.mockRejectedValue(new Error('Network error'));

      renderRegisterPage();

      await user.type(screen.getByLabelText(/first name/i), 'John');
      await user.type(screen.getByLabelText(/last name/i), 'Doe');
      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/mobile number/i), '9876543210');
      await user.type(screen.getByLabelText(/password/i), 'password123');
      
      const termsCheckbox = screen.getByRole('checkbox');
      await user.click(termsCheckbox);

      const submitButton = screen.getByRole('button', { name: /sign up/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Registration failed. Please try again.')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderRegisterPage();

      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      renderRegisterPage();

      expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
      expect(screen.getByRole('checkbox')).toBeInTheDocument();
    });

    it('should have proper dialog accessibility', async () => {
      renderRegisterPage();

      await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
      
      const verifyEmailButton = screen.getByRole('button', { name: /verify email/i });
      await user.click(verifyEmailButton);

      await waitFor(() => {
        const dialog = screen.getByRole('dialog');
        expect(dialog).toBeInTheDocument();
      });
    });
  });
});
