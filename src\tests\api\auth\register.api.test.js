/**
 * API Tests for Registration Authentication
 * Tests all registration-related API endpoints and user creation flows
 */

import axios from 'axios';

// Mock axios for API testing
jest.mock('axios');
const mockedAxios = axios;

describe('Register API Tests', () => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
  const registerEndpoint = `${baseURL}/auth/register`;

  // Mock data
  const mockDonorRegistration = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    mobileNumber: '**********',
    password: 'password123',
    role: 'donor',
    termsAccepted: true,
    ipAddress: '***********',
  };

  const mockNGORegistration = {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    mobileNumber: '**********',
    password: 'password123',
    role: 'ngo',
    organizationName: 'Help Foundation',
    organizationType: 'Non-Profit',
    registrationNumber: 'REG123456',
    termsAccepted: true,
    ipAddress: '***********',
  };

  const mockRegistrationResponse = {
    success: true,
    message: 'Registration successful',
    data: {
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'donor',
        isActive: false,
        emailVerified: false,
        mobileVerified: false,
        createdAt: '2024-01-15T10:00:00Z',
      },
      verificationRequired: {
        email: true,
        mobile: true,
      },
      nextSteps: [
        'Verify your email address',
        'Verify your mobile number',
        'Complete your profile',
      ],
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register', () => {
    it('should register donor successfully', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockRegistrationResponse,
        status: 201,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(registerEndpoint, mockDonorRegistration);

      expect(mockedAxios.post).toHaveBeenCalledWith(registerEndpoint, mockDonorRegistration);
      expect(response.status).toBe(201);
      expect(response.data).toEqual(mockRegistrationResponse);
      expect(response.data.success).toBe(true);
      expect(response.data.data.user.role).toBe('donor');
      expect(response.data.data.user.emailVerified).toBe(false);
    });

    it('should register NGO successfully', async () => {
      const ngoResponse = {
        ...mockRegistrationResponse,
        data: {
          ...mockRegistrationResponse.data,
          user: {
            ...mockRegistrationResponse.data.user,
            email: '<EMAIL>',
            firstName: 'Jane',
            lastName: 'Smith',
            role: 'ngo',
            organizationId: 'org-123',
          },
          organization: {
            id: 'org-123',
            name: 'Help Foundation',
            type: 'Non-Profit',
            registrationNumber: 'REG123456',
            status: 'pending_verification',
          },
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: ngoResponse,
        status: 201,
        headers: { 'content-type': 'application/json' }
      });

      const response = await axios.post(registerEndpoint, mockNGORegistration);

      expect(response.data.data.user.role).toBe('ngo');
      expect(response.data.data.organization.name).toBe('Help Foundation');
      expect(response.data.data.organization.status).toBe('pending_verification');
    });

    it('should handle email already exists error', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: {
            success: false,
            message: 'Email address already registered',
            code: 'EMAIL_EXISTS',
            data: {
              email: '<EMAIL>',
              registeredAt: '2024-01-10T10:00:00Z',
            },
          }
        }
      });

      try {
        await axios.post(registerEndpoint, mockDonorRegistration);
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.code).toBe('EMAIL_EXISTS');
        expect(error.response.data.data.email).toBe('<EMAIL>');
      }
    });

    it('should handle mobile number already exists error', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: {
            success: false,
            message: 'Mobile number already registered',
            code: 'MOBILE_EXISTS',
            data: {
              mobileNumber: '**********',
            },
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          email: '<EMAIL>',
        });
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.code).toBe('MOBILE_EXISTS');
      }
    });

    it('should validate required fields', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'firstName', message: 'First name is required' },
              { field: 'lastName', message: 'Last name is required' },
              { field: 'email', message: 'Email is required' },
              { field: 'password', message: 'Password is required' },
              { field: 'termsAccepted', message: 'Terms and conditions must be accepted' },
            ],
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          role: 'donor',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
        expect(error.response.data.errors).toHaveLength(5);
      }
    });

    it('should validate email format', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid email format',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'email', message: 'Please enter a valid email address' },
            ],
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          email: 'invalid-email',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
      }
    });

    it('should validate password strength', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Password does not meet requirements',
            code: 'WEAK_PASSWORD',
            data: {
              requirements: [
                'At least 8 characters long',
                'Contains at least one uppercase letter',
                'Contains at least one lowercase letter',
                'Contains at least one number',
              ],
              violations: [
                'Password is too short',
                'Missing uppercase letter',
              ],
            },
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          password: '123',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('WEAK_PASSWORD');
        expect(error.response.data.data.violations).toHaveLength(2);
      }
    });

    it('should validate mobile number format', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid mobile number format',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'mobileNumber', message: 'Mobile number must be 10 digits' },
            ],
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          mobileNumber: '123',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('VALIDATION_ERROR');
      }
    });

    it('should validate NGO-specific fields', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'NGO registration validation failed',
            code: 'VALIDATION_ERROR',
            errors: [
              { field: 'organizationName', message: 'Organization name is required' },
              { field: 'organizationType', message: 'Organization type is required' },
              { field: 'registrationNumber', message: 'Registration number is required' },
            ],
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          role: 'ngo',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.errors).toHaveLength(3);
      }
    });

    it('should handle terms not accepted error', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Terms and conditions must be accepted',
            code: 'TERMS_NOT_ACCEPTED',
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          termsAccepted: false,
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('TERMS_NOT_ACCEPTED');
      }
    });

    it('should handle rate limiting', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 429,
          data: {
            success: false,
            message: 'Too many registration attempts',
            code: 'RATE_LIMITED',
            data: {
              retryAfter: 300,
              maxAttempts: 5,
            },
          },
          headers: {
            'retry-after': '300',
          },
        }
      });

      try {
        await axios.post(registerEndpoint, mockDonorRegistration);
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data.code).toBe('RATE_LIMITED');
        expect(error.response.headers['retry-after']).toBe('300');
      }
    });

    it('should handle server errors', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: {
            success: false,
            message: 'Internal server error during registration',
            code: 'REGISTRATION_ERROR',
          }
        }
      });

      try {
        await axios.post(registerEndpoint, mockDonorRegistration);
      } catch (error) {
        expect(error.response.status).toBe(500);
        expect(error.response.data.code).toBe('REGISTRATION_ERROR');
      }
    });
  });

  describe('Email Verification', () => {
    it('should send email verification successfully', async () => {
      const verificationResponse = {
        success: true,
        message: 'Verification email sent successfully',
        data: {
          email: '<EMAIL>',
          verificationId: 'verify-123',
          expiresAt: '2024-01-15T11:00:00Z',
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: verificationResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/send-email-verification`, {
        email: '<EMAIL>',
      });

      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe('<EMAIL>');
      expect(response.data.data.verificationId).toBeDefined();
    });

    it('should verify email with valid token', async () => {
      const verifyResponse = {
        success: true,
        message: 'Email verified successfully',
        data: {
          email: '<EMAIL>',
          verifiedAt: '2024-01-15T10:30:00Z',
          userId: 'user-123',
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: verifyResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/verify-email`, {
        email: '<EMAIL>',
        token: 'verification-token-123',
      });

      expect(response.data.success).toBe(true);
      expect(response.data.data.verifiedAt).toBeDefined();
    });

    it('should handle invalid verification token', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid or expired verification token',
            code: 'INVALID_VERIFICATION_TOKEN',
          }
        }
      });

      try {
        await axios.post(`${baseURL}/auth/verify-email`, {
          email: '<EMAIL>',
          token: 'invalid-token',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('INVALID_VERIFICATION_TOKEN');
      }
    });
  });

  describe('Mobile Verification', () => {
    it('should send mobile OTP successfully', async () => {
      const otpResponse = {
        success: true,
        message: 'OTP sent successfully',
        data: {
          mobileNumber: '**********',
          otpId: 'otp-123',
          expiresAt: '2024-01-15T10:10:00Z',
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: otpResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/send-mobile-otp`, {
        mobileNumber: '**********',
      });

      expect(response.data.success).toBe(true);
      expect(response.data.data.mobileNumber).toBe('**********');
      expect(response.data.data.otpId).toBeDefined();
    });

    it('should verify mobile OTP successfully', async () => {
      const verifyResponse = {
        success: true,
        message: 'Mobile number verified successfully',
        data: {
          mobileNumber: '**********',
          verifiedAt: '2024-01-15T10:30:00Z',
          userId: 'user-123',
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: verifyResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/verify-mobile`, {
        mobileNumber: '**********',
        otp: '123456',
      });

      expect(response.data.success).toBe(true);
      expect(response.data.data.verifiedAt).toBeDefined();
    });

    it('should handle invalid OTP', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid OTP',
            code: 'INVALID_OTP',
            data: {
              attemptsRemaining: 2,
              maxAttempts: 3,
            },
          }
        }
      });

      try {
        await axios.post(`${baseURL}/auth/verify-mobile`, {
          mobileNumber: '**********',
          otp: '000000',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('INVALID_OTP');
        expect(error.response.data.data.attemptsRemaining).toBe(2);
      }
    });

    it('should handle expired OTP', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'OTP has expired',
            code: 'OTP_EXPIRED',
            data: {
              canResend: true,
            },
          }
        }
      });

      try {
        await axios.post(`${baseURL}/auth/verify-mobile`, {
          mobileNumber: '**********',
          otp: '123456',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('OTP_EXPIRED');
        expect(error.response.data.data.canResend).toBe(true);
      }
    });
  });

  describe('Account Activation', () => {
    it('should activate account successfully', async () => {
      const activationResponse = {
        success: true,
        message: 'Account activated successfully',
        data: {
          userId: 'user-123',
          activatedAt: '2024-01-15T10:30:00Z',
          isActive: true,
          canLogin: true,
        },
      };

      mockedAxios.post.mockResolvedValue({ 
        data: activationResponse,
        status: 200,
      });

      const response = await axios.post(`${baseURL}/auth/activate`, {
        userId: 'user-123',
        activationToken: 'activation-token-123',
      });

      expect(response.data.success).toBe(true);
      expect(response.data.data.isActive).toBe(true);
      expect(response.data.data.canLogin).toBe(true);
    });

    it('should handle invalid activation token', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            success: false,
            message: 'Invalid activation token',
            code: 'INVALID_ACTIVATION_TOKEN',
          }
        }
      });

      try {
        await axios.post(`${baseURL}/auth/activate`, {
          userId: 'user-123',
          activationToken: 'invalid-token',
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.code).toBe('INVALID_ACTIVATION_TOKEN');
      }
    });

    it('should handle already activated account', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: {
            success: false,
            message: 'Account is already activated',
            code: 'ALREADY_ACTIVATED',
            data: {
              activatedAt: '2024-01-10T10:00:00Z',
            },
          }
        }
      });

      try {
        await axios.post(`${baseURL}/auth/activate`, {
          userId: 'user-123',
          activationToken: 'token-123',
        });
      } catch (error) {
        expect(error.response.status).toBe(409);
        expect(error.response.data.code).toBe('ALREADY_ACTIVATED');
      }
    });
  });

  describe('Security Features', () => {
    it('should include security headers in response', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: mockRegistrationResponse,
        status: 201,
        headers: { 
          'content-type': 'application/json',
          'x-frame-options': 'DENY',
          'x-content-type-options': 'nosniff',
          'strict-transport-security': 'max-age=********',
        }
      });

      const response = await axios.post(registerEndpoint, mockDonorRegistration);

      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
    });

    it('should handle suspicious registration attempts', async () => {
      mockedAxios.post.mockRejectedValue({
        response: {
          status: 403,
          data: {
            success: false,
            message: 'Suspicious registration activity detected',
            code: 'SUSPICIOUS_ACTIVITY',
            data: {
              requiresAdditionalVerification: true,
              verificationMethods: ['phone', 'document'],
            },
          }
        }
      });

      try {
        await axios.post(registerEndpoint, {
          ...mockDonorRegistration,
          ipAddress: '*******', // Suspicious IP
        });
      } catch (error) {
        expect(error.response.status).toBe(403);
        expect(error.response.data.code).toBe('SUSPICIOUS_ACTIVITY');
      }
    });

    it('should track registration analytics', async () => {
      mockedAxios.post.mockResolvedValue({ 
        data: {
          ...mockRegistrationResponse,
          data: {
            ...mockRegistrationResponse.data,
            analytics: {
              registrationSource: 'web',
              referrer: 'google.com',
              userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
              registrationTime: '2024-01-15T10:00:00Z',
            },
          },
        },
        status: 201,
      });

      const response = await axios.post(registerEndpoint, {
        ...mockDonorRegistration,
        source: 'web',
        referrer: 'google.com',
      });

      expect(response.data.data.analytics.registrationSource).toBe('web');
      expect(response.data.data.analytics.referrer).toBe('google.com');
    });
  });
});
